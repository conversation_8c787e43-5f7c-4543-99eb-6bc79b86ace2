<template>
  <div class="bridge-special">
    <div class="special-c">
      <MapView :padding="[50, 50, 50, -250]" ref="mpRef"></MapView>
    </div>
    <section class="special-l">
      <CockpitCard title="监测结构物统计" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <fitness-index></fitness-index>
      </CockpitCard>
      <CockpitCard title="分布情况" :w="isBig ? '10vw' : '20vw'" h="calc(40vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <Echarts :option="dbOption" v-if="dbOption" height="40vh" key="jkKey" />
      </CockpitCard>
      <CockpitCard title="结构物基本信息" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :isDtl="false">
        <div class="base-info">
          <div class="info-item">
            <img src="@/assets/cockpit/info.png" class="img" />
            <div class="info-data">
              <span>龙江特大桥</span>
              <span>主桥上部结构类型：悬索桥</span>
              <span>技术状况：2类</span>
              <span>按跨径分类：特大桥</span>
            </div>
            <img src="@/assets/cockpit/addres-info.png" class="img-icon">
          </div>
          <div class="divider"></div>
          <div class="info-item">
            <img src="@/assets/cockpit/info.png" class="img" />
            <div class="info-data">
              <span>龙江特大桥</span>
              <span>主桥上部结构类型：悬索桥</span>
              <span>技术状况：2类</span>
              <span>按跨径分类：特大桥</span>
            </div>
            <img src="@/assets/cockpit/addres-info.png" class="img-icon">
          </div>
        </div>
      </CockpitCard>
    </section>
    <section class="special-r">
      <div class="info">
        <div>
          <CockpitCard title="健康度等级" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="levelOption" v-if="levelOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="报警状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <div class="warning-status">
              <div class="warning-status-list" v-for="(item, index) in warningList" :key="'warning' + index">
                <img :src="item.img" class="img" />
                <span>{{ item.name }}</span>
              </div>
            </div>
          </CockpitCard>
          <CockpitCard title="监测类别" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <div class="monitoring-category">
              <div class="monitoring-category-list" v-for="(item, index) in categoryList" :key="'category' + index">
                <img :src="item.img" class="img" />
                <span class="name">{{ item.name }}</span>
                <span class="val">({{ item.val }})</span>
              </div>
            </div>
          </CockpitCard>
        </div>
        <div class="ml-2">
          <CockpitCard title="技术状况" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="jsOption" v-if="jsOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="传感器运营状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="cgOption" v-if="cgOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="监测数据统计" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <Echarts :option="mlOption" v-if="mlOption" height="25vh" key="jkKey" />
            <div class="jc-data-statistics">
              <div class="statistics-info">
                <span class="info-today"></span>
                <span class="info-val">今日数据: <strong>16.8</strong></span>
                <span class="info-unit">亿条</span>
              </div>

              <div class="statistics-info">
                <span class="info-yesterday"></span>
                <span class="info-val">昨日数据: <strong>31.1</strong></span>
                <span class="info-unit">亿条</span>
              </div>
            </div>
          </CockpitCard>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Echarts from '../echarts/echarts.vue';
import Tables from "../tables.vue";
import MapView from '../mapView.vue';
import FitnessIndex from '../fitness/index.vue';

export default {
  name: 'Special',
  components: {
    CockpitCard,
    Echarts,
    Tables,
    MapView,
    FitnessIndex,
  },
  data() {
    return {
      map: null,
      isBig: isBigScreen(),
      jsOption: null,
      mlOption: null,
      dbOption: null,
      levelOption: null, // 桥梁技术状况评定
      cgOption: null, // 传感器运营状态
      warningList: [
        {
          name: '一级',
          color: '#FA5151',
          img: require('@/assets/cockpit/health/level-1.png'),
        },
        {
          name: '二级',
          color: '#FF9803',
          img: require('@/assets/cockpit/health/level-2.png'),
        },
        {
          name: '三级',
          color: '#F9E21F',
          img: require('@/assets/cockpit/health/level-3.png'),
        },
      ],
      categoryList: [
        {
          name: '温湿度',
          val: 216,
          img: require('@/assets/cockpit/health/humiture.png'),
        },
        {
          name: '衬砌变形',
          val: 86,
          img: require('@/assets/cockpit/health/lining.png'),
        },
        {
          name: '裂缝',
          val: 149,
          img: require('@/assets/cockpit/health/crack.png'),
        },
        {
          name: '应变',
          val: 1737,
          img: require('@/assets/cockpit/health/strain.png'),
        },
        {
          name: '倾斜',
          val: 26,
          img: require('@/assets/cockpit/health/tilt.png'),
        },
        {
          name: '体外预应力',
          val: 28,
          img: require('@/assets/cockpit/health/prestress.png'),
        },
        {
          name: '位移',
          val: 890,
          img: require('@/assets/cockpit/health/displacement.png'),
        },
        {
          name: '振动',
          val: 28,
          img: require('@/assets/cockpit/health/vibrate.png'),
        },
        {
          name: '雨量',
          val: 2,
          img: require('@/assets/cockpit/health/hyetal.png'),
        },
        {
          name: '结构温度',
          val: 32,
          img: require('@/assets/cockpit/health/temperature.png'),
        },
        {
          name: '索力',
          val: 28,
          img: require('@/assets/cockpit/health/cable-force.png'),
        },
        {
          name: '转角',
          val: 62,
          img: require('@/assets/cockpit/health/corner.png'),
        },
        {
          name: '地震',
          val: 40,
          img: require('@/assets/cockpit/health/earthquake.png'),
        },
        {
          name: '车辆荷载',
          val: 28,
          img: require('@/assets/cockpit/health/vehicle-load.png'),
        },
        {
          name: '风速、风向',
          val: 28,
          img: require('@/assets/cockpit/health/wind.png'),
        },
      ],
      obj: {
        "deptIds": [],
        "id": "1815935561620328449",
        icon: require('@/assets/cockpit/bridge.png'),
        "paramsDTO": {
          "precisionParams": {
            "base.span_classify_type": "1"
          }
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
    })
    this.initBarsCharts();
    this.initDbBarECharts();
    // 获取健康度等级 图标数据
    let leveData = [96, 62, 90];
    let levelXData = ["等级Ⅰ", "等级Ⅱ", "等级Ⅲ"];
    let levelColorObj = {
      barColor0: 'rgba(0,185,254,0.1)',
      barColor6: 'rgba(0,185,254,0.6)',
      barColor: 'rgba(0,185,254,1)',
      barTColor: 'rgba(132,222,255,1)',
    }
    this.levelOption = this.initBEcharts(levelXData, leveData, levelColorObj);
    // 获取技术状况 图标数据
    let jsData = [54, 62, 90];
    let jsXData = ["1类", "2类", "3类"];
    let jsColorObj = {
      barColor0: 'rgba(2,255,131,0.1)',
      barColor6: 'rgba(2,255,131,0.6)',
      barColor: 'rgba(2,255,131,1)',
      barTColor: 'rgba(143,255,201,1)',
    }
    this.jsOption = this.initBEcharts(jsXData, jsData, jsColorObj);

    this.initCGEcharts();
  },
  destroyed() {
    if(this.$refs.mpRef) {
      this.$refs.mpRef.removeVector('桥梁');
    }
  },
  methods: {
    initMap() {
      this.$nextTick(() => {
        // this.$refs.mpRef.setVector('健康监测', this.obj)
      })
    },
    initBarsCharts() {
      let xData = ["四月", "五月", "六月", "七月", "八月", "九月"];
      let data = [580, 820, 860, 560, 810, 900];

      let mlSColor = '#00FDFD';
      let mlEColor = 'rgba(0,253,253,0.1)';
      this.mlOption = this.initBarCharts(xData, data, mlSColor, mlEColor);
    },
    initBarCharts(xData = [], data = [], startColor = '', endColor = '', legend = []) {

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "3%",
          right: "4%",
          top: "25%",
          bottom: "1%",
          containLabel: true,
        },
        legend: {
          show: legend.length > 0 ? true : false,
          data: legend,
          x: 'center',
          y: '2%',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
          textStyle: {
            color: '#ffffff',
          },
        },
        xAxis: {
          data: xData,
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          axisLabel: {
            color: "#fff",
            fontSize: 14,
            // rotate: 40,
            interval: false,
          },
        },
        yAxis: {
          name: "",
          nameTextStyle: {
            color: "#999999",
            fontSize: 13,
          },
          axisLabel: {
            color: "#999999",
            fontSize: 13,
            formatter: '{value}'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          interval: 200,
          max: 1000,
        },
        series: [
          {
            name: legend.length ? legend[0] : "",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: startColor,
                    },
                    {
                      offset: 0.8,
                      color: endColor,
                    },
                  ],
                  false
                ),
              },
            },
            data,
          },
        ],
      };
      return option;
    },
    initDbBarECharts() {
      let xData = ["保山管理处", "大理管理处", "昆明东管理处", "昆明西管理处", "丽江管理处", "临沧管理处", "普洱管理处", "曲靖管理处", "文山管理处", "西双版纳管理处", "昭通管理处", "红河管理处"]
      this.dbOption = {
        backgroundColor: "rgba(255,255,255,0)",
        legend: {
          show: true,
          x: "center",
          y: "2%",
          textStyle: {
            color: "#fff",
          },
          data: ["桥梁数量", "隧道数量", "边坡数量"],
        },
        grid: {
          left: "3%",
          right: "3%",
          top: "10%",
          bottom: "3%",
          containLabel: true,
        },

        tooltip: {
          show: "true",
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        xAxis: {
          type: "value",
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          axisLabel: {
            color: "#fff",
            fontSize: 12,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
        },
        yAxis: [
          {
            type: "category",
            axisTick: {
              show: true,
              length: -5,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(110,112,121,0.5)",
              },
            },
            axisLabel: {
              color: "#fff",
              fontSize: 12,
            },
            splitArea: {
              show: false,
            },
            data: xData,
          },
          {
            type: "category",
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: xData,
          },
        ],
        series: [
          {
            name: "桥梁数量",
            type: "bar",
            barWidth: 5,
            itemStyle: {
              normal: {
                show: true,
                color: "#5589FF",
                barBorderRadius: 0,
                borderWidth: 0,
                borderColor: "#333",
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(85,137,255,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(85,137,255,0)",
                  },
                ]),
              },
            },
            barGap: 0.5,
            barCategoryGap: "40%",
            data: [
              207, 232, 160, 204, 255, 228, 221, 204, 195, 192, 192, 176
            ],
          },
          {
            name: "隧道数量",
            type: "bar",
            barWidth: 5,
            itemStyle: {
              normal: {
                show: true,
                color: "#08FF85",
                barBorderRadius: 0,
                borderWidth: 0,
                borderColor: "#333",
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(8,253,134,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(8,253,134,0)",
                  },
                ]),
              },
            },
            barGap: 0.5,
            barCategoryGap: "40%",
            data: [
              247, 286, 289, 267, 298, 237, 311, 226, 238, 302, 294, 210
            ],
          },
          {
            name: "边坡数量",
            type: "bar",
            barWidth: 5,
            itemStyle: {
              normal: {
                show: true,
                color: "#27A0FF",
                barBorderRadius: 0,
                borderWidth: 0,
                borderColor: "#333",

                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(38,158,255,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(38,158,255,0)",
                  },
                ]),
              },
            },
            barGap: 0.5,
            barCategoryGap: "40%",
            data: [60, 73, 70, 69, 71, 72, 54, 54, 72, 54, 61, 79],
          },
        ],
      };

    },
    initBEcharts(xData, data, colorObj) {
      const sideData = data.map((item) => item + 1);

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          top: "15%",
          left: "3%",
          right: "3%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: {
          data: xData,
          //坐标轴
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
        },
        yAxis: {
          //坐标轴
          axisLine: {
            show: false,
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#999999",
            },
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          interval: 10,
        },
        series: [
          {
            name: "a",
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                fontSize: 16,
                color: '#fff',
                offset: [0, -10],
              },
            },
            type: "bar",
            barWidth: 24.5,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: colorObj.barColor0, // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: colorObj.barColor6, // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: colorObj.barColor, // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            data: data,
            barGap: 0,
          },
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: colorObj.barColor0, // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: colorObj.barColor6, // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: colorObj.barColor, // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: sideData,
          },
          {
            name: "b",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 1,
              borderColor: colorObj.barTColor,
              color: colorObj.barTColor,
            },

            symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
            symbolSize: ["30", "12"],
            symbolOffset: ["0", "-8"],
            symbolRotate: -15,
            symbolPosition: "end",
            data: data,
            z: 3,
          },
        ],
      };
      return option;
    },
    initCGEcharts() {
      var dataStyle = {
        normal: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          shadowBlur: 0,
          shadowColor: '#203665'
        }
      };
      let radius = this.isBig ? [80, 90] : [40, 50]
      this.cgOption = {
        backgroundColor: "rgba(0,0,0,0)",
        series: [{
          name: '第一个圆环',
          type: 'pie',
          clockWise: false,
          radius: radius || [40, 50],
          itemStyle: dataStyle,
          hoverAnimation: false,
          center: ['18%', '50%'],
          data: [{
            value: 84,
            label: {
              normal: {
                rich: {
                  a: {
                    color: '#FFFFFF',
                    align: 'center',
                    fontSize: 20,
                    fontWeight: "bold"
                  },
                  b: {
                    color: '#fff',
                    align: 'center',
                    fontSize: 16
                  }
                },
                formatter: (params) => {
                  return "\n\n\n\n\n{a|" + params.value + "%}" + "\n\n\n\n\n{b|设备在线率}";
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '14',
                  fontWeight: 'normal',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#90EF7F',
                shadowColor: '#90EF7F',
                shadowBlur: 0,
                borderRadius: 10,
              }
            }
          }, {
            value: 16,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(23,116,255,0.2)'
              },
              emphasis: {
                color: 'rgba(23,116,255,0.2)'
              }
            }
          }]
        }, {
          name: '第二个圆环',
          type: 'pie',
          clockWise: false,
          radius: radius || [40, 50],
          itemStyle: dataStyle,
          hoverAnimation: false,
          center: ['50%', '50%'],
          data: [{
            value: 99,
            label: {
              normal: {
                rich: {
                  a: {
                    color: '#fff',
                    align: 'center',
                    fontSize: 20,
                    fontWeight: "bold"
                  },
                  b: {
                    color: '#fff',
                    align: 'center',
                    fontSize: 16
                  }
                },
                formatter: function (params) {
                  return "\n\n\n\n\n{a|" + params.value + "%}" + "\n\n\n\n\n{b|数据完整率}";
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '14',
                  fontWeight: 'normal',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#2196F3',
                shadowColor: '#2196F3',
                shadowBlur: 0,
                borderRadius: 10,
              }
            }
          }, {
            value: 1,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(23,116,255,0.2)'
              },
              emphasis: {
                color: 'rgba(23,116,255,0.2)'
              }
            }
          }]
        }, {
          name: '第三个圆环',
          type: 'pie',
          clockWise: false,
          radius: radius || [40, 50],
          itemStyle: dataStyle,
          hoverAnimation: false,
          center: ['82%', '50%'],
          data: [{
            value: 99,
            label: {
              normal: {
                rich: {
                  a: {
                    color: '#fff',
                    align: 'center',
                    fontSize: 20,
                    fontWeight: "bold"
                  },
                  b: {
                    color: '#fff',
                    align: 'center',
                    fontSize: 16
                  }
                },
                formatter: function (params) {
                  return "\n\n\n\n\n{a|" + params.value + "%}" + "\n\n\n\n\n{b|数据完整率}";
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '14',
                  fontWeight: 'normal',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#A569FF',
                shadowColor: '#A569FF',
                shadowBlur: 0,
                borderRadius: 10,
              }
            }
          }, {
            value: 1,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(23,116,255,0.2)'
              },
              emphasis: {
                color: 'rgba(23,116,255,0.2)'
              }
            }
          }]
        }]
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-special {
  width: 100%;
  height: 100%;
  position: relative;
  margin-top: vwpx(15px);

  .special-c {
    width: 100%;
    height: 91vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .special-l {
    position: absolute;
    left: vwpx(30px);
    top: 0;

    .base-info {
      padding: vwpx(10px) vwpx(20px);

      .info-item {
        display: flex;
        align-items: center;

        .img {
          width: vwpx(120px);
          height: vwpx(140px);
        }

        .img-icon {
          width: vwpx(68px);
          height: vwpx(68px);
          margin-left: auto;
          cursor: pointer;
        }

        .info-data {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(28px);
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-left: vwpx(30px);

          display: flex;
          flex-direction: column;

          span:first-child {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 500;
            font-size: vwpx(30px);
            color: #00B9FE;
            line-height: vwpx(40px);
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-bottom: vwpx(12px);
            cursor: pointer;
          }
        }
      }

      .divider {
        border: 1px dotted rgba(156, 189, 255, 0.5);
        margin: vwpx(20px) 0;
      }
    }
  }

  .special-r {
    position: absolute;
    right: vwpx(30px);
    top: 0;

    .info {
      display: flex;

      .warning-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: vwpx(20px);
        flex-wrap: wrap;

        .warning-status-list {
          margin: 0 vwpx(10px) vwpx(20px) vwpx(10px);
          width: 25%;
          height: 25%;
          display: flex;
          align-items: center;
          flex-direction: column;

          .img {
            width: 60%;
            height: 60%;
          }

          span {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 500;
            font-size: vwpx(28px);
            color: #FFFFFF;
            text-align: center;
            font-style: normal;
            margin-top: vwpx(10px);
          }
        }
      }
    }

    .jc-data-statistics {
      position: absolute;
      top: vwpx(5px);
      left: 0;
      right: 0;
      width: 100%;

      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .statistics-info {
        margin-bottom: vwpx(10px);
        display: flex;
        align-items: center;
        justify-content: center;

        .info-today {
          display: block;
          width: vwpx(20px);
          height: vwpx(20px);
          background: #02FF83;
        }

        .info-yesterday {
          display: block;
          width: vwpx(20px);
          height: vwpx(20px);
          background: #FFBE27;
        }

        .info-val {
          font-weight: 400;
          font-size: vwpx(26px);
          color: #FFFFFF;
          margin: 0 vwpx(20px);
        }

        .info-unit {
          font-weight: 400;
          font-size: vwpx(26px);
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .monitoring-category {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: vwpx(20px);

      .monitoring-category-list {
        width: 50%;
        margin-bottom: vwpx(15px);
        display: flex;
        align-items: center;

        .img {
          width: vwpx(50px);
          height: vwpx(50px);
        }

        .name {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(30px);
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin: 0 vwpx(20px);
        }

        .val {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(30px);
          color: #F2AF4A;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>