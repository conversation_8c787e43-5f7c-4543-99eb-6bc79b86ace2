<template>
  <div class="health-monitoring" :style="{ paddingTop: top ? top : type == 2 ? '1.2vh' : '' }">
    <div v-for="(item, index) in list" :key="index" class="list" @click="onItemClick(item, index)"
      :style="{ height: isBig ? '40.5%' : '39%', cursor: type == 2 ? 'pointer' : '' }">
      <div :class="cIndex === index && type == 2 && isAct ? 'item-act-bg' : 'item-bg'"></div>
      <div class="list-item">
        <span class="value" :style="setStyle(index)">
          {{ item.value }} <small>{{ item.unit }}</small>
        </span>
        <span class="name">{{ item.name }}</span>
        <!-- <div class="dotted-divider"></div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { isBigScreen } from '../../util/utils';
import { getWhetherHealth, getStructureTypeNunber } from "@/api/cockpit/index";

export default {
  name: "HealthMonitoring",
  props: {
    type: {
      type: String,
      default: "1", // 1默认，2桥梁、边坡、隧道
    },
    top: {
      type: [Number, String],
      default: '',
    },
    isAct: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      bridgeId: '12153',
      cIndex: 0,
      list: [
        {
          name: "特大桥",
          value: 0,
          unit: "座",
          color: "#00BAFF",
        },
        {
          name: "大桥",
          value: 0,
          unit: "座",
          color: "#FFB346",
        },
        {
          name: "特长隧道",
          value: 0,
          unit: "座",
          color: "#FFEA00",
        },
        {
          name: "长隧道",
          value: 0,
          unit: "座",
          color: "#00EAFF",
        },
        {
          name: "边坡",
          value: 0,
          unit: "座",
          color: "#8148FE",
        },
      ],
    };
  },
  methods: {},
  components: {},
  mounted() { },
  methods: {
    onItemClick(item, index) {
      this.cIndex = index;
      this.$emit('click', item);
    },
    setStyle(index) {
      return { color: this.list[index].color };
    },
    getListgetWhetherHealth() {
      //getStructureTypeNunber
      getStructureTypeNunber({ bridgeId: this.bridgeId }).then(res => {
        if (res.code == 200) {


          if (this.type == 2) {
            let qlObj = res.result.桥梁 || {};
            // Calculate the sum of all bridge values
            let bridgeTotal = 0;
            // Loop through all properties in qlObj and add numeric values
            for (const key in qlObj) {
              if (Object.hasOwnProperty.call(qlObj, key) && typeof qlObj[key] === 'number') {
                bridgeTotal += qlObj[key];
              }
            }
            let tunnelObj = res.result.隧道 || {};
            // Calculate the sum of all bridge values
            let tunnelTotal = 0;
            // Loop through all properties in qlObj and add numeric values
            for (const key in tunnelObj) {
              if (Object.hasOwnProperty.call(tunnelObj, key) && typeof tunnelObj[key] === 'number') {
                tunnelTotal += tunnelObj[key];
              }
            }
            let slopeObj = res.result.边坡 || {};
            // Calculate the sum of all bridge values
            let slopeTotal = 0;
            // Loop through all properties in qlObj and add numeric values
            for (const key in slopeObj) {
              if (Object.hasOwnProperty.call(slopeObj, key) && typeof slopeObj[key] === 'number') {
                slopeTotal += slopeObj[key];
              }
            }
            this.list = [
              {
                name: "桥梁",
                value: bridgeTotal,
                unit: "座",
                color: "#00B8FA",
              },
              {
                name: "边坡",
                value: slopeTotal,
                unit: "座",
                color: "#EE9433",
              },
              {
                name: "隧道",
                value: tunnelTotal,
                unit: "座",
                color: "#9749FF",
              },
            ]
          } else {
            this.list[0].value = res.result.桥梁.特大桥
            this.list[1].value = res.result.桥梁.大桥
            this.list[2].value = res.result.隧道.特长隧道
            this.list[3].value = res.result.隧道.长隧道
            this.list[4].value = res.result.边坡.边坡
          }
        }
      });
    },
  },
  components: {
  },
  mounted() {


  },
  created() {
    this.getListgetWhetherHealth()
    window.$Bus.$on('aMapClick', async (data) => {
      if (data.sys_dept_id) return
      this.bridgeId = data.id
      this.getListgetWhetherHealth()
    })
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.health-monitoring {
  font-family: Microsoft YaHei UI, Microsoft YaHei UI;
  padding: 0 vwpx(16px) vwpx(16px) vwpx(16px);
  color: #ffffff;
  height: 100%;
  overflow-y: auto;

  display: flex;
  flex-wrap: wrap;

  .list {
    width: 31%;
    height: vwpx(135px);
    // background: rgba(0,0,0,0.1);
    // box-shadow: inset 0px 0px 10px 0px #0065FF;
    // border-radius: 6px;
    // border: 1px solid #20A9FF;
    margin: 2% 1.1%;
    position: relative;

    .list-item {
      padding: 0 vwpx(22px);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      z-index: 9;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .name {
        // font-family: Microsoft YaHei UI, Source Han Sans;
        font-weight: 700;
        font-size: 1.4vh;
        color: #ffffff;
        text-align: center;
        // font-style: normal;
        // text-transform: none;
      }

      .value {
        // font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(40px);
        color: #42abff;
        text-align: center;
        // font-style: normal;
        // text-transform: none;

        small {
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.5);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .item-bg {
      position: absolute;
      bottom: vwpx(-20px);
      left: 5%;
      width: 90%;
      height: vwpx(90px);
      // border: vwpx(2px) solid #027fba;

      // transform: skew(-10deg) perspective(1000px) rotateX(45deg)
      //   translateY(20px);
      // transform-origin: center bottom;
      // background: linear-gradient(
      //   180deg,
      //   rgba(2, 127, 186, 0.2) 0%,
      //   rgba(2, 127, 186, 0.1) 100%
      // );
      // box-shadow: 0 0 20px rgba(2, 127, 186, 0.3);
      z-index: 1;
      background: url("~@/assets/cockpit/b-bg.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .item-act-bg {
      position: absolute;
      bottom: vwpx(-20px);
      left: 5%;
      width: 90%;
      height: vwpx(90px);

      z-index: 1;
      background: url("~@/assets/cockpit/act-bg.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}

.dotted-divider {
  width: 100%;
  height: vwpx(2px);
  background: repeating-linear-gradient(to right,
      rgba(23, 116, 255, 0.3),
      rgba(23, 116, 255, 0.3) vwpx(2px),
      transparent vwpx(3px),
      transparent vwpx(8px));
  margin-top: vwpx(7px);
}
</style>
