<template>
  <div class="img-view">
    <img :src="imgUrl" alt="" v-for="(imgUrl, index) in imgUrlList" :key="index"
      :style="{ width: isBig ? width*2 + 'px' : width + 'px', height: isBig ? height*2 + 'px' : height + 'px' }" />
  </div>
</template>

<script>
import { findFiles } from '@/api/file/index.js'
import { isBigScreen } from './util';

export default {
  props: {
    id: {
      type: [Number, String],
      default: ''
    },
    width: {
      type: [Number, String],
      default: 18
    },
    height: {
      type: [Number, String],
      default: 18
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      imgUrlList: [],
    }
  },
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.getImgById(val)
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    getImgById(id) {
      findFiles({ ownerId: id }).then(res => {
        if (res.code === 200 && res.data) {
          this.imgUrlList = res.data.map(file => file.url);
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.img-view {
  margin-right: 6px;
}
</style>