<template>
	<div class="background" :style="oneMap ? 'background:unset' : 'background: #f0f0f0;'">
		<!-- 浮动标题 -->
		<div class="floating-title" v-if="showFloatingTitle && floatingTitle">
			{{ floatingTitle }}
		</div>
		<el-row :gutter="20" class="row-flex">
			<el-col
				class="col-flex"
				:xs="24"
				:sm="12"
				:md="8"
				:lg="8"
				v-for="(cardI, index) in cardList"
				:key="'card-' + cardI.id"
			>
				<transition :name="getTransitionName(index)">
					<card
						class="card-item"
						v-show="shouldShowCard(cardI.id)"
						:form="cardI"
						:card-id="cardI.id"
						:is-upload="isUpload"
						:style="oneMap ? 'background-color:unset' : ''"
						@refresh="handleRefresh"
						@updateBox="updateProcess"
					></card>
				</transition>
			</el-col>
			<el-col v-if="!isUpload" :xs="24" :sm="12" :md="8" :lg="6">
				<addCard :type-id="typeId" @refresh="handleRefresh"></addCard>
			</el-col>
		</el-row>

		<!-- 固定在右下角的 div -->
		<div class="fixed-divs" :style="oneMap ? 'right: 16%' : 'right: 16%;'">
			<el-tag class="fixed-div" type="success">
				进度为: {{ fileMapLength }} / {{ listLength }}
			</el-tag>
			<el-tag class="fixed-div">文件数量: {{ selfFileNum }}</el-tag>
			<el-tag class="fixed-div" type="info">
				文件大小: {{ selfTotalFileSize | formatFileSize }}
			</el-tag>
		</div>
	</div>
</template>

<script>
import { getListByEntity, saveModuleFile } from '@/api/system/moduleFile.js'
import { moduleFiles, getOneByCode } from '@/api/system/moduleType.js'
import card from '@/components/ModuleFile/card.vue'
import addCard from '@/components/ModuleFile/addCard.vue'
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex'
export default {
	name: 'cardBox',
	inject: ['oneMap'],
	components: {
		card,
		addCard,
	},
	props: {
		typeCode: {
			type: String,
			default: '',
		},
		isUpload: {
			type: Boolean,
			default: true,
		},
		bizId: {
			type: String,
			default: '0',
		},
		showFileName: {
			type: Boolean,
			default: true,
		},
		forView: {
			type: Boolean,
			default: false,
		},
		showFloatingTitle: {
			type: Boolean,
			default: true,
		},
		assetType: {
			type: Number,
			default: null,
		},
	},
	provide() {
		return {
			isUpload: this.isUpload,
			bizId: this.bizId,
			showFileName: this.showFileName,
			forView: this.forView,
			assetType: this.assetType,
		}
	},
	data() {
		return {
			cardList: [
				// ...更多卡片
			],
			typeId: null,
			fileMapLength: 0,
			listLength: 0,
			selfFileNum: 0,
			selfTotalFileSize: 0,
			floatingTitle: '',
		}
	},
	methods: {
		...mapMutations('file', {
			resetList: 'RESET_LIST',
		}),
		async fetchList() {
			if (this.typeId == null) {
				try {
					const res = await getOneByCode(this.typeCode)
					this.typeId = res.data.id
					this.floatingTitle = res.data.name
				} catch (error) {
					console.error('获取typeId错误:', error)
					return
				}
			}

			try {
				const res = await getListByEntity({ typeId: this.typeId })
				this.cardList = res.data
			} catch (error) {
				console.error('获取文件模块错误 :', error)
			}
		},

		handleRefresh() {
			this.resetList()
			this.fetchList()
		},
		shouldShowCard(cardId) {
			return this.cardsFromVuex[cardId] && this.cardsFromVuex[cardId].showComponent
		},
		getTransitionName(index) {
			const transitions = ['el-zoom-in-center', 'el-zoom-in-top', 'el-zoom-in-bottom']
			return transitions[index % transitions.length] // 循环使用这三种过渡效果
		},
		updateProcess() {
			this.listLength = this.getListLength()
			this.fileMapLength = this.getFileMapLength()
			this.selfTotalFileSize = this.getTotalFileSize()
			this.selfFileNum = this.getFileNum()
		},
	},
	mounted() {
		this.fetchList()
	},
	watch: {
		bizId: {
			handler: 'resetList',
			immediate: true,
		},
	},
	computed: {
		...mapState('file', {
			cardsFromVuex(state) {
				return state.cards || []
			},
		}),
		...mapGetters('file', {
			getListLength: 'getListLength',
			getFileMapLength: 'getFileMapLength',
			getTotalFileSize: 'getTotalFileSize',
			getFileNum: 'getFileNum',
		}),
	},
}
</script>

<style scope>
.background {
	height: 95vh; /* 使背景填满整个视口高度 */
	display: flex;
	flex-direction: column;
	padding: 20px;
	box-sizing: border-box;
	position: relative; /* 使子元素相对于这个容器定位 */
}

.row-flex {
	flex: 1; /* 使 row-flex 填满剩余空间 */
	overflow-y: auto; /* 超出部分显示滚动条 */
	display: flex;
	flex-wrap: wrap;
	align-items: flex-start;
}

.col-flex {
	margin-bottom: 20px;
}

::v-deep .card-item {
	height: 300px; /* 固定高度，可以根据需要调整 */
	overflow-y: auto; /* 超出高度时添加垂直滚动条 */
	background: white; /* 确保背景色 */
	padding: 10px; /* 内边距，可根据需要调整 */
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
	border-radius: 4px; /* 圆角 */
}

.card-container {
	padding-right: 30px;
}

.card-item:hover {
	transform: scale(1.05); /* 鼠标悬停时放大 */
}

/* 取消最后一行的下边距 */
.col-flex:last-child {
	margin-bottom: 0;
}

.card {
	margin-bottom: 20px;
}
.floating-title {
	position: absolute; /* 相对于 .background 容器定位 */
	top: 10px; /* 距离 .background 容器顶部20px */
	left: 50%;
	transform: translateX(-50%);
	background: rgba(17, 79, 248, 0.3);
	color: white;
	padding: 10px;
	border-radius: 4px;
	pointer-events: none;
	z-index: 1000; /* 确保浮动标题在最上层 */
}
/* 固定在右下角的 div 样式 */
.fixed-divs {
	position: fixed;
	top: calc(6vh + 10px);
	pointer-events: none;
}

.fixed-div {
	background: rgba(0, 0, 0, 0.5);
	color: white;
	padding-left: 10px;
	padding-right: 10px;
	margin-top: 10px;
	margin-right: 2px;
	margin-left: 2px;
	border-radius: 4px;
}
</style>
