export const getMin = (arr) => {
  if(arr.length === 0){
      return 0;
  }
  let tmpValue = arr[0];
  arr.forEach((value, index) => {
      if(tmpValue > value){
          tmpValue = value
      }
  })
  return tmpValue
}

export const getMax = (arr) => {
  if(arr.length === 0){
      return 0;
  }
  let tmpValue = arr[0];
  arr.forEach((value, index) => {
      if(tmpValue < value){
          tmpValue = value
      }
  })
  return tmpValue
}

export const url = 'https://jkjc.glyhgl.com:22586/bigScreen/structure/analysisConfig/draw'