<template>

  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">


      <el-descriptions title="养护路段简要信息" size="mini" border>
        <el-descriptions-item label="养护路段名称">
          <el-tag size="small">{{ data.maintenanceSectionName }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="管养单位">{{ data.departmentName }}</el-descriptions-item>
        <el-descriptions-item label="公路路段">{{ data.roadSectionName }}</el-descriptions-item>
        <el-descriptions-item label="统一里程起点桩号">{{ data.unifiedMileagePileStart }}</el-descriptions-item>
        <el-descriptions-item label="统一里程终点桩号">{{ data.unifiedMileagePileEnd }}</el-descriptions-item>
        <el-descriptions-item label="路线等级">
          <dict-tag :options="dict.type.sys_route_grade" :value="data.routeGrade"/>
        </el-descriptions-item>
        <!--        <el-descriptions-item label="起点名称">placeStartName</el-descriptions-item>-->
        <!--        <el-descriptions-item label="终点名称">placeEndName</el-descriptions-item>-->
        <!--        <el-descriptions-item label="主线里程">mainLength</el-descriptions-item>-->
        <!--        <el-descriptions-item label="统一里程起点桩号">unifiedMileagePileStart</el-descriptions-item>-->
        <!--        <el-descriptions-item label="统一里程终点桩号">unifiedMileagePileEnd</el-descriptions-item>-->
        <!--        <el-descriptions-item label="通车时间">openingTime</el-descriptions-item>-->
        <!--        <el-descriptions-item label="路段长度">roadSectionLength</el-descriptions-item>-->

      </el-descriptions>

<!--      <el-descriptions title="养护路段子段信息">-->


<!--      </el-descriptions>-->
      <div style="margin: 0 10%">

        <el-divider >养护路段子段信息</el-divider>
      </div>

      <!--数据表格开始-->

      <el-table size="mini" height="calc(100vh - 310px)" border :data="routeSegmentsList">
        <el-table-column label="序号"   align="center" type="index" width="60">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>

        <el-table-column label="子段名称"  width="180" :show-overflow-tooltip="true" align="center" prop="routeSegmentsName"/>

        <el-table-column label="管养单位" :show-overflow-tooltip="true" align="center" prop="maintenanceUnitName"
                         width="180"/>

        <el-table-column v-if="false" label="养护路段" :show-overflow-tooltip="true" align="center"
                         prop="maintenanceSectionName" width="120"/>
        <el-table-column label="路线" :show-overflow-tooltip="true" align="center" prop="baseRouteName" width="120"/>

        <el-table-column label="统计里程" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         width="120"/>
        <el-table-column label="主线里程" :show-overflow-tooltip="true" align="center" prop="mainLength"
                         :formatter="(...arg)=>{if(arg[2]) return arg[2].toLocaleString()}"/>
        <el-table-column label="路线等级" :show-overflow-tooltip="true" align="center" prop="roadGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.roadGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="路段类型" :show-overflow-tooltip="true" align="center" prop="roadType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_type" :value="scope.row.roadType"/>
          </template>
        </el-table-column>
        <el-table-column label="路面类型" :show-overflow-tooltip="true" align="center" prop="roadSurfaceType"
                         width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_surface_type" :value="scope.row.roadSurfaceType"/>
          </template>
        </el-table-column>
        <el-table-column label="车道数" :show-overflow-tooltip="true" align="center" prop="lanes"/>
        <el-table-column label="通车时间" :show-overflow-tooltip="true" align="center" prop="openingTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.openingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="起点桩号" :show-overflow-tooltip="true" align="center" prop="pileStart"/>
        <el-table-column label="终点桩号" :show-overflow-tooltip="true" align="center" prop="pileEnd"/>
        <el-table-column label="起点名称" :show-overflow-tooltip="true" align="center" prop="placeStartName"/>
        <el-table-column label="终点名称" :show-overflow-tooltip="true" align="center" prop="placeEndName"/>
        <el-table-column label="统一里程起点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统一里程终点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="运营状态" :show-overflow-tooltip="true" align="center" prop="state" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_operation_state" :value="scope.row.state"/>
          </template>
        </el-table-column>

      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-drawer>
</template>

<script>

import {listRouteSegments} from "@/api/system/routeSegments";

export default {
  name: "maintenaceDetail",
  dicts: ['sys_route_type', 'sys_route_grade', 'sys_surface_type', 'sys_operation_state'],
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      // 养护子段管理表格数据
      routeSegmentsList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        maintenanceSectionId: null,
      },
      // 总条数
      total: 0,

    }
  },
  watch: {
    data() {
      this.queryParams.maintenanceSectionId = this.data.maintenanceSectionId
      this.getList()
    }
  },
  methods: {
    /** 查询养护子段管理列表 */
    getList() {
      listRouteSegments(this.queryParams).then(response => {
        this.routeSegmentsList = response.rows;
        this.total = response.total;
      });
    },
  },
};
</script>
<style scoped>
.container {
  margin-top: -20px;
  padding: 20px;
}
</style>
