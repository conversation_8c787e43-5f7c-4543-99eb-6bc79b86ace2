<template>
	<PageContainer>
		<template slot="search">
			<div style="display: flex; align-items: center; margin: 0">
				<CascadeSelection
					style="min-width: 192px"
					:form-data="queryParams"
					v-model="queryParams"
					types="201"
					multiple
				/>
				<el-select
					style="margin: 0 20px"
					v-model="queryParams.typeId"
					placeholder="资产名称"
					@change="getList"
					clearable
				>
					<el-option
						v-for="item in typeOptions"
						:key="item.id"
						:label="item.typeName"
						:value="item.id"
					/>
				</el-select>
				<el-button
					v-hasPermi="['baseData:roadbedStatistics:getStatistics']"
					type="primary"
					icon="el-icon-search"
					@click="handleQuery"
				>
					搜索
				</el-button>
				<el-button icon="el-icon-refresh" @click="reSet">重置</el-button>
			</div>
		</template>
		<template slot="header">
			<el-button
				v-hasPermi="['baseData:roadbedStatistics:export']"
				style="margin-bottom: 10px"
				type="primary"
				@click="exportList"
			>
				导出报表
			</el-button>
		</template>
		<template slot="body">
			<!-- <el-table
				v-adjust-table
				ref="table"
				v-loading="loading"
				height="100%"
				border
				:data="tableData"
				:header-cell-style="{ height: '36px' }"
				:row-class-name="tableRowClassName"
			>
				<el-table-column label="序号" type="index" width="50" align="center">
					<template v-slot="scope">
						{{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
					</template>
				</el-table-column>
				<el-table-column
					label="管养处"
					align="center"
					prop="managementMaintenanceName"
					min-width="120"
					show-overflow-tooltip
				/>
				<el-table-column
					label="管养分处"
					align="center"
					prop="managementMaintenanceBranchName"
					min-width="120"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线编码"
					align="center"
					prop="routeCode"
					min-width="120"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路段名称"
					align="center"
					prop="maintenanceSectionName"
					min-width="120"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线名称"
					align="center"
					prop="routeName"
					min-width="120"
					show-overflow-tooltip
				/>
				<el-table-column label="资产类型" align="center" prop="assetTypeName" min-width="120" />
				<el-table-column label="资产名称" align="center" prop="typeName" min-width="120" />
				<el-table-column label="数量(km)" align="center" prop="totalLength" min-width="120">
					<template slot-scope="{ row }">
						<span v-if="row.managementMaintenanceName === '合计' && !queryParams.typeId">
							{{ row.totalLength }}
						</span>
						<el-link v-else type="primary" :underline="false" @click="handleShowTable(row)">
							{{ row.totalLength }}
						</el-link>
					</template>
				</el-table-column>
			</el-table> -->

			<el-table
				v-adjust-table
				ref="table"
				height="100%"
				style="width: 100%"
				:header-cell-style="{ height: '36px' }"
				border
				v-loading="loading"
				:data="tableData"
				:row-class-name="tableRowClassName"
			>
				<el-table-column label="序号" type="index" width="50" align="center">
					<template v-slot="scope">
						<span v-if="scope.row.maintenanceSectionName !== '合计'">
							{{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
						</span>
					</template>
				</el-table-column>
				<el-table-column
					label="养护路段"
					align="center"
					prop="maintenanceSectionName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线编码"
					align="center"
					prop="routeCode"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线名称"
					align="center"
					prop="routeName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="资产主类"
					align="center"
					prop="assetMainTypeName"
					min-width="140"
					show-overflow-tooltip
				></el-table-column>

				<el-table-column
					label="资产子类"
					align="center"
					prop="assetTypeName"
					min-width="140"
					show-overflow-tooltip
				/>

				<el-table-column
					label="统计数据参数"
					align="center"
					prop="statisticsValue"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }" v-if="row.statisticsValue">
						<el-link
							v-if="row.maintenanceSectionName === '合计' && !queryParams.typeId"
							:underline="false"
							style="pointer-events: none"
						>
							{{ row.statisticsValue + row.statisticsUnit }}
						</el-link>
						<el-link v-else type="primary" :underline="false" @click="handleShowTable(row)">
							{{ row.statisticsValue + row.statisticsUnit }}
						</el-link>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				:pageSizes="[10, 20, 30, 50, 100, 1000]"
				@pagination="getList"
			/>
		</template>
		<DetailTable v-if="showTable" :showTable="showTable" :row="row" @close="closeTable" />
	</PageContainer>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { getType, getStatistics } from '@/api/baseData/subgrade/statistics'
import { listFacilityStatisticsRecords } from '@/api/baseData/afforest/statistic/index'
import DetailTable from './components/DetailTable.vue'

export default {
	name: 'Statistics',
	props: {},
	components: { CascadeSelection, DetailTable },
	data() {
		return {
			queryParams: {
				pageNum: 1,
				pageSize: 20,
			},
			total: 0,
			tableData: [],
			loading: false,
			typeOptions: [],
			showTable: false,
			row: {},
		}
	},
	created() {
		this.getTypeOptions()
		this.getList()
	},
	methods: {
		handleQuery() {
			this.queryParams.pageNum = 1
			this.queryParams.pageSize = 50
			this.getList()
		},
		// 重置搜索条件
		reSet() {
			this.queryParams = {
				pageNum: 1,
				pageSize: 20,
			}
			this.handleQuery()
		},
		getTypeOptions() {
			getType({ mainTypeId: '2' }).then((res) => {
				this.typeOptions = res
			})
		},
		getList() {
			this.loading = true
			this.tableData = []
			this.queryParams.mainTypeId = 2
			listFacilityStatisticsRecords(this.queryParams)
				.then((res) => {
					if (res.code === 200) {
						if (res.rows.length > 0 && this.queryParams.typeId) {
							let sum = 0
							let maintenanceSectionIds = []
							let routeCodes = []
							res.rows.map((el) => {
								sum += Number(el.statisticsValue)
								if (el.maintenanceSectionId) {
									maintenanceSectionIds.push(el.maintenanceSectionId)
								}
								if (el.routeCode) {
									routeCodes.push(el.routeCode)
								}
							})
							const formattedSum = sum.toFixed(3).replace(/\.?0+$/, '');
							res.rows.push({
								maintenanceSectionName: '合计',
								maintenanceSectionIds: maintenanceSectionIds,
								routeCodes: routeCodes,
								statisticsUnit: res.rows[0].statisticsUnit || 'km',
								statisticsValue: formattedSum,
							})
						}
						this.tableData = res.rows
						this.total = res.total
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		handleShowTable(row) {
			if (row.totalLength == 0) return
			if (row.maintenanceSectionName == '合计') {
				this.showTable = true
				this.row = {
					...row,
					typeId: this.queryParams.typeId,
				}
			} else {
				this.showTable = true
				this.row = row
			}
		},
		closeTable() {
			this.showTable = false
			this.row = {}
		},
		tableRowClassName(row_params) {
			let { row } = row_params
			if (row.maintenanceSectionName == '合计') {
				return `tr-fixed fixed-row`
			} else {
				return ``
			}
		},
		exportList() {
			this.$modal
				.confirm('导出所有表格数据，是否继续？')
				.then(() => {
					this.download(
						'/baseData/roadbed/statistics/export',
						this.queryParams,
						`桥梁统计_${new Date().getTime()}.xlsx`,
						{
							headers: { 'Content-Type': 'application/json;' },
							parameterType: 'body',
						}
					)
				})
				.catch(() => {})
		},
	},
	computed: {},
	watch: {},
}
</script>

<style lang="scss" scoped>
::v-deep .el-table {
	.tr-fixed {
		display: table-row;
		position: sticky;
		bottom: 0;
		width: 100%;
		td {
			border-top: 1px solid #f3f5fa;
			background: #fff;
		}
	}
	.fixed-row {
		bottom: 0;
	}
}
</style>
