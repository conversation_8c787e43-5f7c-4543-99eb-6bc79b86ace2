<template>
  <div class="maindiv">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form ref="elForm" :rules="rules" :inline="true" :model="formData" label-width="88px">
          <el-form-item label="月份" prop="month">
            <el-date-picker
              v-model="formData.month"
              placeholder="选择月份"
              style="width: 250px"
              type="month"
              format="M"
              value-format="M">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="里程桩号" prop="mileStr">
            <el-input v-model="formData.mileStr" clearable placeholder="请输入里程桩号" style="width: 250px">
            </el-input>
          </el-form-item>
          <el-form-item label="总费" prop="fund">
            <el-input v-model="formData.fund" clearable placeholder="请输入总费" style="width: 250px">
            </el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" clearable placeholder="请输入备注" rows="3" style="width: 250px"
                      type="textarea">
            </el-input>
          </el-form-item>
          <el-form-item label="附件" prop="">
            <file-upload v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="18">
        <div class="mb10">
          <el-button type="primary" @click="handleAdd">新增</el-button>
          <el-button type="primary" v-if="['1', '3'].includes(params.typeId)" v-has-menu-permi="['operate:detail:upload']" @click="upload.open=true">导入</el-button>
        </div>
        <el-table v-adjust-table
          ref="dataTable"
          v-loading="loading"
          :data="tableData"
          border
          height="375px"
          highlight-current-row
          row-key="id"
          size="mini"
          stripe
          style="width: 100%"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            width="50"
          />
          <template v-for="(column,index) in columns">
            <el-table-column v-if="column.visible"
                             :label="column.label"
                             :prop="column.field"
                             align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-if="column.canInput">
                  <el-select  v-if="column.field == 'isMarketization'" v-model="scope.row.isMarketization">
                    <el-option label="是" value="1"></el-option>
                    <el-option label="否" value="2"></el-option>
                  </el-select>
                  <el-input v-else v-model="scope.row[column.field]" type="number"
                            @change="callMethod(column.change, scope.row, column)"/>
                </template>
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            label="操作"
            width="50"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleRemove(scope.row)"
              >移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="mt20" style="text-align: right">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-col>
    </el-row>
    <el-dialog v-if="addDetail" :visible.sync="addDetail" append-to-body title="添加明细" width="90%">
      <component :is="componentName" page-type="view" :maiSecId="maiSecId" style="height: 70vh" @check="handleCheck"></component>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :data="upload.data"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ThreeElectricity from '../settings/threeElectricity'
import ThreelOilElectricity from '../settings/threelOilElectricity'
import TunnelElectricity from '../settings/tunnelElectricity'
import TunnelOilElectricity from '../settings/tunnelOilElectricity'
import VehicleUsage from '../settings/vehicleUsage'
import {
  addMeteringDetail,
  editMeteringDetail,
  listMeteringDetailFund
} from "@/api/calculate/operationManageFee/enterFee";
import {v4 as uuidv4} from "uuid";
import {getToken} from "@/utils/auth";

export default {
  components: {
    ThreeElectricity, ThreelOilElectricity, TunnelElectricity, TunnelOilElectricity, VehicleUsage,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: {
    addType: {
      type: String,
      default: '隧道电费'
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailRow: {
      type: Object,
      default: () => {
        return {}
      }
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  watch: {
    detailRow: {
      handler(val) {
        if (val.id) {
          this.formData = JSON.parse(JSON.stringify(val))
          this.formData.month = String(this.formData.month)
          if (!this.formData.fileId) this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
          this.handleQuery()
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      loading: false,
      addDetail: false,
      rules: {
        month: [
          { required: true, message: '请选择月份', trigger: 'change' }
        ],
        mileStr: [
          { required: true, message: '请输入里程桩号', trigger: 'blur' }
        ],
        fund: [
          { required: true, message: '请输入总费', trigger: 'blur' }
        ]
      },
      componentName: 'TunnelElectricity',
      columns: [],
      column1: [
        {key: 0, width: 100, field: 'houseNum', label: `户号`, visible: true},
        {key: 1, width: 100, field: 'houseName', label: `用户名`, visible: true},
        {key: 2, width: 100, field: 'powerRange', label: `用电范围`, visible: true},
        {
          key: 3,
          width: 100,
          field: 'num',
          label: `本期电量`,
          visible: true,
          canInput: 'true',
          change: 'handleChangeNum'
        },
        {
          key: 4,
          width: 100,
          field: 'bqFund',
          label: `本期金额`,
          visible: true,
          canInput: 'true',
          change: 'handleChangeAmount'
        },
        {
          key: 5,
          width: 100,
          field: 'price',
          label: `本期单价`,
          visible: true,
          canInput: 'true',
          change: 'handleChangePrice'
        },
        {key: 6, width: 100, field: 'avgPrice', label: `电网代购平均电价`, visible: true, canInput: 'true'},
        {key: 7, width: 100, field: 'isMarketization', label: `是否市场化用电`, visible: true, canInput: 'true'},
        {
          key: 8,
          width: 100,
          field: 'waterFund',
          label: `本期水利基金`,
          visible: true,
          canInput: 'true',
          change: 'handleChangeWaterFund'
        },
        {
          key: 9,
          width: 100,
          field: 'fund',
          label: `本期总额`,
          visible: true,
          canInput: 'true',
          change: 'handleChangeFund'
        },
      ],
      column2: [
        {key: 0, width: 100, field: 'siteName', label: `站点`, visible: true},
        {key: 1, width: 100, field: 'useScope', label: `使用范围`, visible: true},
        {
          key: 2,
          width: 100,
          field: 'fund',
          label: `本期金额`,
          visible: true,
          canInput: 'true',
          change: 'handleChangeFund'
        },
      ],
      column3: [
        {key: 0, width: 100, field: 'siteName', label: `站点`, visible: true},
        {key: 1, width: 100, field: 'carNum', label: `车牌号`, visible: true},
        {key: 2, width: 100, field: 'carType', label: `车辆类型`, visible: true},
        {
          key: 3,
          width: 100,
          field: 'fund',
          label: `本期金额`,
          visible: true,
          canInput: 'true',
          change: 'handleChangeFund'
        },
      ],
      formData: {
        fileId: uuidv4().replace(/-/g, '').slice(0, 20)
      },
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/manager/operate/metering/detail/uploadExcel",
        data: {
          type: this.params.typeId
        }
      },
    }
  },
  mounted() {
    console.log(this.params)
    this.columns = []
    switch (this.addType) {
      case '隧道电费':
        this.columns = this.column1
        this.componentName = 'TunnelElectricity'
        break
      case '三大系统电费':
        this.columns = this.column1
        this.componentName = 'ThreeElectricity'
        break
      case '隧道发电油费':
        this.columns = this.column2
        this.componentName = 'TunnelOilElectricity'
        break
      case '三大系统发电油费':
        this.columns = this.column2
        this.componentName = 'ThreelOilElectricity'
        break
      case '车辆使用费':
        this.columns = this.column3
        this.componentName = 'VehicleUsage'
        break
    }
    this.formData.meterId = this.params.meterId
    this.formData.companyType = this.params.companyType
    this.formData.typeId = this.params.typeId
  },
  methods: {
    handleQuery() {
      this.queryParams.detailId = this.detailRow.id
      this.queryParams.typeId = this.detailRow.typeId

      // 获取明细
      listMeteringDetailFund(this.queryParams).then(res => {
        res.rows.forEach(data => {
          data.isMarketization = String(data.isMarketization || '')
          delete data.id
        })
        this.tableData = res.rows
      })
    },
    handleAdd() {
      this.addDetail = true
    },
    handleCheck(datas) {
      datas.forEach(data => {
        data.dataId = data.id
        delete data.id
        if (!this.tableData.some(item => item.dataId === data.dataId)) {
          this.tableData.push(data);
        }
      })
      this.addDetail = false
    },
    handleRemove(row) {
      this.tableData = this.tableData.filter(item => item.dataId !== row.dataId);
    },
    callMethod(methodName, row, column) {
      if (this[methodName]) {
        this[methodName](row, column);
      }
    },
    handleChangeFund(row, colum) {
      this.$set(this.formData, 'fund', this.tableData.reduce((total, item) => {
        return total + (Number(item.fund) || 0);
      }, 0))
    },
    handleChangeNum(row, colum) {
      // 更新金额
      this.$set(row, 'bqFund', this.ceilToTwo(row.price * row.num))
      this.handleChangeAmount(row, colum)
    },
    handleChangeAmount(row, colum) {
      // 更新单价
      if (!row.price) {
        this.$set(row, 'price', this.ceilToTwo(row.bqFund / row.num))
      }
      // 更新总额
      this.$set(row, 'fund', this.ceilToTwo(Number(row.bqFund) + (Number(row.waterFund) || 0)))
      this.handleChangeFund(row, colum)
    },
    handleChangePrice(row, colum) {
      if (row.num) {
        // 更新金额
        this.$set(row, 'bqFund', this.ceilToTwo(row.price * row.num))
        this.handleChangeAmount(row, colum)
      }
    },
    handleChangeWaterFund(row, colum) {
      // 更新总额
      this.$set(row, 'fund', this.ceilToTwo((Number(row.bqFund) || 0) + Number(row.waterFund)))
      this.handleChangeFund(row, colum)
    },
    handleSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.detailFundList = this.tableData
        // 校验tableData中字段有没有空的
        // let isEmpty = this.tableData.some(item => {
        //   return Object.keys(item).some(key => {
        //     return !item[key]
        //   })
        // })
        // if (isEmpty) {
        //   this.$message.error('请填写完整明细')
        //   return
        // }
        if (
          this.formData.fileId &&
          Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length > 0
        ) {
          this.formData.fileId = this.formData.fileId[0];
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        if (this.formData.id) {
          editMeteringDetail(this.formData).then(res => {
            if (res.code === 200) {
              this.$message.success('保存成功')
              this.$emit('close')
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          addMeteringDetail(this.formData).then(res => {
            if (res.code === 200) {
              this.$message.success('保存成功')
              this.$emit('close')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('manager/operate/metering/detail/download', {}, `电费导入.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      response.data.forEach(item => item.isMarketization = String(item.isMarketization || ''))
      this.tableData.push(...response.data);
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  }
}

</script>


<style lang="scss" scoped>
::v-deep .el-input--mini .el-input__inner {
  padding: 0px !important;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
