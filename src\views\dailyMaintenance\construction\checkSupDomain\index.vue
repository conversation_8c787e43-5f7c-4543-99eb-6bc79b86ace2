<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :page-list="queryConstructionAuditList" :dis-stage="5">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              v-has-menu-permi="['checkSupDomain:construction:export']"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
      <template #operate="scope" >
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleRegis(scope.scope.row)"
        >监理审核
        </el-button>
      </template>
    </registration-list>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" :title="drawerTitle" destroy-on-close :visible.sync="drawer" size="70%">
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-drawer>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {queryConstructionAuditList} from "@/api/dailyMaintenance/construction/checkSupDomain";
import Detail from "./detail.vue";
export default {
  name: "CheckSupDomain",
  components: {Detail, registrationList},
  data() {
    return {
      drawerTitle: '监理审核',
      drawer: false,
      rowData: {}
    }
  },
  methods: {
    queryConstructionAuditList,
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.$refs.regis.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null

      this.download(
          'manager/checkDomain/export',
          {...this.$refs.regis.queryParams},
          `checkDomain_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
