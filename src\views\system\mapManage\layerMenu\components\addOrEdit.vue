<template>
  <el-form ref="form" :model="ruleForm" :rules="rules" label-width="100px">
    <el-row :gutter="20" :style="{ pointerEvents: readonly ? 'none' : '' }">
      <el-col :span="12" :offset="0">
        <el-form-item label="父级" prop="parentId">
          <!-- <el-select v-model="ruleForm.parentId" placeholder="请选择父级" clearable style="width: 100%;"
            @change="handleParentChange">
            <el-option :label="item.name" :value="item.id" v-for="(item, index) in parentOptions" :key="index" />
          </el-select> -->
          <el-cascader v-model="ruleForm.parentId" :options="parentOptions"
            :props="{ checkStrictly: true, label: 'name', value: 'id', children: 'child' }" clearable style="width: 100%;"
            @change="handleCascaderChange" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="目录名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入目录名称" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" :style="{ pointerEvents: readonly ? 'none' : '' }">
      <el-col :span="12" :offset="0">
        <!-- <el-form-item label="目录类型" prop="menuType">
          <el-select v-model="ruleForm.menuType" placeholder="请选择目录类型" clearable style="width: 100%;">
            <el-option label="基础数据目录" :value="1" />
            <el-option label="分析图层目录" :value="2" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="一张图显示" prop="oneMapShow">
          <el-select v-model="ruleForm.oneMapShow" placeholder="请选择" clearable style="width: 100%;">
            <el-option label="显示" :value="1" />
            <el-option label="不显示" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">

        <el-form-item label="排序" prop="showIndex">
          <el-input-number v-model="ruleForm.showIndex" :min="0" :max="9999" label="请输入描述"
            :style="{ pointerEvents: readonly ? 'none' : '' }" style="width: 100%;" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="目录展示类型" prop="menuShowType">
          <el-select v-model="ruleForm.menuShowType" placeholder="请选择" clearable style="width: 100%;">
            <el-option label="都显示" :value="0" />
            <el-option label="树形显示" :value="1" />
            <el-option label="数据总览" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="描述" prop="description">
          <el-input v-model="ruleForm.description" type="textarea" :readonly="readonly" :rows="2" placeholder="请输入描述" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item style="margin: auto;display: flex;justify-content: center;">
      <el-button type="primary" @click="onSubmit" v-loading="loading" v-if="!readonly">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { getListTree, addLayerMenu, updateLayerMenu } from "@/api/oneMap/layerMenu";

export default {
  props: {
    form: {
      type: Object,
      default: {},
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleForm: {
        oneMapShow: 1,
        showIndex: 0,
      },
      rules: {
        menuName: [
          { required: true, message: '请输入目录名称', trigger: 'blur' },
        ],
        menuType: [
          { required: true, message: '请选择目录类型', trigger: ['change', 'blur'] },
        ],
      },
      loading: false,
      parentOptions: []
    }
  },
  created() {
    this.ruleForm = { ...this.form }
    this.getList();
  },
  methods: {
    // 获取所有列表数据
    getList() {
      getListTree({ pageNum: 1, pageSize: 9999 }).then(res => {
        this.parentOptions = res || []
        if (res.code == 200) {
          this.parentOptions = res.rows || []
        }
      })
      // getListPage({pageNum:1,pageSize: 9999}).then(res => {
      //   this.parentOptions = res || []
      //   if (res.code == 200) {
      //     this.parentOptions = res.rows || [];
      //   }
      // })
    },
    // 提交
    onSubmit() {
      this.ruleForm.parentId =
        Object.prototype.toString.call(this.ruleForm.parentId) ===
          '[object Array]'
          ? this.ruleForm.parentId[0]
          : this.ruleForm.parentId
      this.$refs.form.validate(vali => {
        if (!vali) return
        let request = this.ruleForm.id ? updateLayerMenu(this.ruleForm) : addLayerMenu(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功';
        this.loading = true;
        request.then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess(msg);
            this.$emit('refresh')
            this.onCancel();
          }
        }).finally(() => {
          this.loading = false;
        })
      })
    },
    // 取消关闭
    onCancel() {
      this.$emit('close', false)
    },

    // 监听父级元素变化
    handleParentChange(e) {
      let arr = this.parentOptions.filter(v => v.id == e)
      if (arr && arr.length > 0) {
        this.ruleForm.menuType = arr[0].menuType
      }
    },
    handleCascaderChange(e) {
      if (e) {
        this.ruleForm.parentId = e[e.length - 1]
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
