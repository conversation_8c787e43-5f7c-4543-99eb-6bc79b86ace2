<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="" prop="inspectionUnitName">
        <el-input
          v-model="queryParams.inspectionUnitName"
          placeholder="请输入检查单位"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="inspectedUnitName">
        <el-input
          v-model="queryParams.inspectedUnitName"
          placeholder="请输入受检单位"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="inspectionTime">
        <el-date-picker clearable
                        style="width: 240px"
                        v-model="queryParams.inspectionTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择检查日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="status">
        <el-select v-model="queryParams.status" style="width: 240px" clearable placeholder="请选择状态">
          <el-option value="待下发" label="待下发"></el-option>
          <el-option value="待接收" label="待接收"></el-option>
          <el-option value="已接收" label="已接收"></el-option>
          <el-option value="已完成" label="已完成"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
        <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
      </el-form-item>
      <br>
      <!--默认折叠-->
      <el-form-item label="" prop="receiveTime" v-show="showSearch">
        <el-date-picker clearable
                        style="width: 240px"
                        v-model="queryParams.receiveTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择接收时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="supervisorName" v-show="showSearch">
        <el-input
          v-model="queryParams.supervisorName"
          placeholder="请输入检查问题责任人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="rectifierName" v-show="showSearch">
        <el-input
          v-model="queryParams.rectifierName"
          placeholder="请输入整改责任人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
    </el-form>
    <!--筛选区结束-->

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-if="false">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['supervise:superviseRecord:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5" v-if="false">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['supervise:superviseRecord:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5" >
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['supervise:superviseRecord:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5" v-if="false">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['supervise:superviseRecord:export']"
        >导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:user:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns" > </right-toolbar>
    </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%"
                ref="dataTable"  highlight-current-row  @row-click="(row)=>$refs.dataTable.toggleRowSelection(row)"
                v-loading="loading" border :data="superviseRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column fixed label="序号" width="50" type="index"/>
        <el-table-column label="检查单位" width="180" align="center" prop="inspectionUnitName" v-if="computedColumns('检查单位')"/>
        <el-table-column label="受检单位" width="180" align="center" prop="inspectedUnitName" v-if="computedColumns('受检单位')"/>
        <el-table-column label="检查日期" width="180" align="center" prop="inspectionTime" v-if="computedColumns('检查日期')">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.inspectionTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="80" prop="status" v-if="computedColumns('状态')">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status.startsWith('已')?'success':''">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="检查内容" width="280" :show-overflow-tooltip="true" align="center" prop="contents"/>
        <el-table-column label="整改要求及建议" width="280" :show-overflow-tooltip="true" align="center"
                         prop="suggestions"/>
        <el-table-column label="检查人" width="180" align="center" prop="inspectionUserNames"/>
        <el-table-column label="检查问题责任人" width="120" align="center" prop="supervisorName"/>
        <el-table-column label="接收时间" width="180" align="center" prop="receiveTime" v-if="computedColumns('接收时间')">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.receiveTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="整改责任人" width="180" align="center" prop="rectifierName" v-if="computedColumns('整改责任人')"/>
        <el-table-column label="备注" width="280" :show-overflow-tooltip="true" align="center" prop="remark" v-if="computedColumns('备注')"/>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="220"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" >
            <div @click.stop>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
              >查看
              </el-button>

              <el-button
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleDownload(scope.row)"
              >下载
              </el-button>
              <el-button
                v-if="false"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['supervise:superviseRecord:edit']"
              >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['supervise:superviseRecord:remove']"
              >删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-search"
                @click="currentRow = scope.row; showDetail = true"
              >详情
              </el-button>
            </div>

          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->

    <!-- 添加或修改督查记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="60%" append-to-body>
      <el-form ref="form" size="small" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="检查单位id" prop="inspectionUnitId">
          <el-input v-model="form.inspectionUnitId" placeholder="请输入检查单位id"/>
        </el-form-item>
        <el-form-item label="检查单位name" prop="inspectionUnitName">
          <el-input v-model="form.inspectionUnitName" placeholder="请输入检查单位name"/>
        </el-form-item>
        <el-form-item label="受检单位id" prop="inspectedUnitId">
          <el-input v-model="form.inspectedUnitId" placeholder="请输入受检单位id"/>
        </el-form-item>
        <el-form-item label="受检单位name" prop="inspectedUnitName">
          <el-input v-model="form.inspectedUnitName" placeholder="请输入受检单位name"/>
        </el-form-item>
        <el-form-item label="检查日期" prop="inspectionTime">
          <el-date-picker clearable
                          style="width: 100%"
                          v-model="form.inspectionTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择检查日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查问题责任人id" prop="supervisorId">
          <el-input v-model="form.supervisorId" placeholder="请输入检查问题责任人"/>
        </el-form-item>
        <el-form-item label="检查问题责任人" prop="supervisorName">
          <el-input v-model="form.supervisorName" placeholder="请输入检查问题责任人"/>
        </el-form-item>
        <el-form-item label="接收时间" prop="receiveTime">
          <el-date-picker clearable
                          style="width: 100%"
                          v-model="form.receiveTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择接收时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="合并后的检查内容">
          <editor v-model="form.contents" :min-height="192"/>
        </el-form-item>
        <el-form-item label="合并后整改要求及建议">
          <editor v-model="form.suggestions" :min-height="192"/>
        </el-form-item>
        <el-form-item label="整改责任人id" prop="rectifierId">
          <el-input v-model="form.rectifierId" placeholder="请输入整改责任人id"/>
        </el-form-item>
        <el-form-item label="整改责任人name" prop="rectifierName">
          <el-input v-model="form.rectifierName" placeholder="请输入整改责任人name"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="待下发">待下发</el-radio>
            <el-radio label="待接收">待接收</el-radio>
            <el-radio label="已接收">已接收</el-radio>
            <el-radio label="已完成">已完成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看督查记录对话框 -->
    <el-dialog title="查看督查记录" :visible.sync="view" width="60%" append-to-body>
      <el-form ref="form" size="small" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查单位" prop="">
              <el-input readonly v-model="form.inspectionUnitName"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受检单位" prop="">
              <el-input readonly v-model="form.inspectedUnitName"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查问题责任人" prop="supervisorName">
              <el-input readonly v-model="form.supervisorName"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改责任人" prop="rectifierName">
              <el-input readonly v-model="form.rectifierName" placeholder="请输入整改责任人name"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查日期" prop="">
              <el-date-picker clearable
                              readonly
                              style="width: 100%"
                              v-model="form.inspectionTime"
                              type="date"
                              value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收日期" prop="receiveTime">
              <el-date-picker clearable
                              readonly
                              style="width: 100%"
                              v-model="form.receiveTime"
                              type="date"
                              value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="检查内容">
          <el-input readonly type="textarea" v-model="form.contents" :rows="6"/>
        </el-form-item>
        <el-form-item label="整改要求及建议">
          <el-input readonly type="textarea" v-model="form.suggestions" :rows="6"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" :rows="3" readonly v-model="form.remark"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :disabled="form.status !== '待下发'" label="待下发">待下发</el-radio>
            <el-radio :disabled="form.status !== '待接收'" label="待接收">待接收</el-radio>
            <el-radio :disabled="form.status !== '已接收'" label="已接收">已接收</el-radio>
            <el-radio :disabled="form.status !== '已完成'" label="已完成">已完成</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="view=false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <SuperviseRecordDetail :visible.sync="showDetail" :data="currentRow" title="督察记录详情" :append-to-body="true"
                           size="55%"/>
  </div>
</template>

<script>
import {
  listSuperviseRecord,
  getSuperviseRecord,
  delSuperviseRecord,
  addSuperviseRecord,
  updateSuperviseRecord
} from "@/api/supervise/superviseRecord";
import {getToken} from "@/utils/auth";
import SuperviseRecordDetail from "./detail.vue"
import axios from "axios";
import {blobValidate} from "@/utils/ruoyi";
import {Loading} from "element-ui";
import {delRepoteForm} from "@/api/repote/repoteForm";

export default {
  name: "SuperviseRecord",
  components: {SuperviseRecordDetail},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 督查记录表格数据
      superviseRecordList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      view: false,

      currentRow: {},
      showDetail: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        inspectionUnitId: null,
        inspectionUnitName: null,
        inspectedUnitId: null,
        inspectedUnitName: null,
        inspectionTime: null,
        supervisorId: null,
        supervisorName: null,
        receiveTime: null,
        contents: null,
        suggestions: null,
        status: null,
        rectifierId: null,
        rectifierName: null,
      },
      // 列信息
      columns: [
        {key: 1, label: `检查单位`, visible: true},
        {key: 3, label: `受检单位`, visible: true},
        {key: 4, label: `检查日期`, visible: true},
        // {key: 5, label: `检查问题责任人`, visible: true},
        {key: 7, label: `接收时间`, visible: true},
        // {key: 8, label: `合并后的检查内容`, visible: true},
        // {key: 9, label: `合并后整改要求及建议`, visible: true},
        {key: 10, label: `状态`, visible: true},
        {key: 11, label: `整改责任人`, visible: true},
        {key: 13, label: `备注`, visible: true}
      ],
      // 表单校验
      rules: {
        inspectionUnitId: [
          {required: true, message: "检查单位id不能为空", trigger: "blur"}
        ],
        inspectionUnitName: [
          {required: true, message: "检查单位name不能为空", trigger: "blur"}
        ],
        inspectedUnitId: [
          {required: true, message: "受检单位id不能为空", trigger: "blur"}
        ],
        inspectedUnitName: [
          {required: true, message: "受检单位name不能为空", trigger: "blur"}
        ],
        inspectionTime: [
          {required: true, message: "检查日期不能为空", trigger: "blur"}
        ],
        status: [
          {required: true, message: "状态(待下发、待接收、已接收、已完成)不能为空", trigger: "change"}
        ],


      }
    };
  },
  computed: {
    computedColumns() {
      return (label) => {
        return this.columns.filter(item => item.label === label)[0].visible;
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listSuperviseRecord(this.queryParams).then(response => {
        this.superviseRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inspectionUnitId: null,
        inspectionUnitName: null,
        inspectedUnitId: null,
        inspectedUnitName: null,
        inspectionTime: null,
        supervisorId: null,
        supervisorName: null,
        receiveTime: null,
        contents: null,
        suggestions: null,
        status: null,
        rectifierId: null,
        rectifierName: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加督查记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSuperviseRecord(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改督查记录";
      });

    },
    /** 修改按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getSuperviseRecord(id).then(response => {
        this.form = response.data;
        this.view = true;
      });

    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSuperviseRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSuperviseRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    // handleDelete(row) {
    //   const id = row.id || this.ids;
    //   this.$modal.confirm('是否确认删除督查记录编号为"' + id + '"的数据项？').then(function () {
    //     return delSuperviseRecord(id);
    //   }).then(() => {
    //     this.getList();
    //     this.$modal.msgSuccess("删除成功");
    //   }).catch(() => {
    //   });
    // },
    handleDelete(row) {
      if (row.id) {
        const id = row.id;
        this.$modal
          .confirm("是否确认删除数据项？")
          .then(function () {
            return delSuperviseRecord(id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          });
      } else {
        if (this.ids.length > 0) {
          this.$modal
            .confirm("是否确认删除选中的" + this.ids.length + "条数据项？")
            .then(() => {
              let delArray = [];
              this.ids.forEach((item) => {
                delArray.push(delSuperviseRecord(item));
              });
              Promise.all(delArray).then(
                () => this.$modal.msgSuccess("删除成功") || this.getList()
              );
            });
        }
      }
    },
    /** 导出按钮操作 */
    handleExport() {

      console.log(this.download)

      this.download('patrol/superviseRecord/export', {
        ...this.queryParams,
        ids: this.ids
      }, `督察记录_${new Date().getTime()}.xlsx`,{
        // parameterType: 'body'
      })

    },
    handleDownload(row) {
      let downloadLoadingInstance = Loading.service({
        text: '正在下载数据，请稍候',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      // axios.get(process.env.VUE_APP_BASE_API + "/supervise/superviseRecord/download/" + row.id, {
      axios.get(process.env.VUE_APP_BASE_API + "/patrol/superviseRecord/download/" + row.id, {
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        responseType: 'blob'
      }).then(res => {
        let filename
        {
          let str = decodeURIComponent(res.headers['content-disposition']).trim()
          let name = str ? str.split(";")[1].trim() : ''
          filename = name ? name.split("=")[1] : '未知的文件名'
        }
        saveAs(res.data, filename)
      }).finally(
        ()=>{
          downloadLoadingInstance.close()
        }
      )
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
<style scoped>
::v-deep .el-dialog__body {
  height: 76vh;
  overflow-y: auto;
}
</style>
