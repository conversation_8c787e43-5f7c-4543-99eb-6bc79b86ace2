<template>
  <div class="app-container">
    <el-row :gutter="20" style="display: flex;">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm"
          :inline="true"
          :model="queryParams"
          :rules="rules"
          label-width="68px"
          size="mini"
        >
          <el-form-item label="" prop="yearList">
            <el-select v-model="queryParams.yearList" placeholder="请选择" multiple :multiple-limit='2' style="width: 240px">
              <el-option
                v-for="item in years"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="domainIdList">
            <select-tree-checkbox
              v-model="queryParams.domainIdList"
              :deptType="100"
              style="width: 240px"
            ></select-tree-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="handleQuery"
            >搜索
            </el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置
            </el-button
            >
            <el-button
              type="success"
              icon="el-icon-download"
              size="mini"
              @click="handleDownload"
            >导出报表
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8" v-loading="loading" style="height: 82vh">
          <iframe v-if="reportData.html" :srcdoc="reportData.html" frameborder="0" style="height: 100%" width="100%"></iframe>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from "axios";
import { getElectricityNumber } from '@/api/calculate/report/comprehensive'
import SelectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox.vue";

export default {
  components: {SelectTreeCheckbox},
  data() {
    return {
      loading: false,
      queryParams: {},
      years: [],
      reportData: {},
      rules: {},
      deptList: []
    }
  },
  created() {
    // 获取近五年
    for (let i = 0; i < 4; i++) {
      this.years.push(new Date().getFullYear() - i)
    }
  },
  methods: {
    handleQuery() {
      if (!this.queryParams.yearList || !this.queryParams.yearList.length || this.queryParams.yearList.length < 2) {
        this.$message.warning('请至少选择两个年份')
        return
      }
      if  (!this.queryParams.domainIdList || !this.queryParams.domainIdList.length) {
        this.$message.warning('请选择部门')
        return
      }
      this.$refs.queryForm.validate(valid => {
        if (!valid) return
        this.loading = true
        getElectricityNumber(this.queryParams).then(res => {
          this.reportData = res.data
        }).finally(() => {
          this.loading = false
        })
      });
    },
    resetQuery() {
      this.queryParams = {}
    },
    handleDownload() {
      if (!this.reportData.downUrl) {
        this.$message.warning('暂无数据')
        return
      }
      axios({
        method: "get",
        responseType: 'arraybuffer',
        url: this.reportData.downUrl,
        headers: {}
      }).then((res) => {
        const arrayBuffer = res.data;
        // 创建一个Blob对象
        const blob = new Blob([arrayBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // 对于.xls文件
        saveAs(blob, this.reportData.fileName)
      })
    },
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding-bottom: 0px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
