<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="140px">
        <el-col :span="12">
          <el-form-item label="计量名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入计量名称" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量编号" prop="code">
            <el-input v-model="formData.code" placeholder="请输入计量编号" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管养单位" prop="domainId">
            <select-tree v-model="formData.domainId" placeholder="请选择管养单位" deptType="201" clearable></select-tree>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路段" prop="maiSecId">
            <road-section v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="请选择路段"></road-section>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请计量单位" prop="calcDomainId">
            <select-tree v-model="formData.calcDomainId" placeholder="请选择申请计量单位" deptType="201" clearable></select-tree>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同" prop="conId">
            <el-input v-model="formData.conId" placeholder="请选择合同" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量日期" prop="calcDate">
            <el-date-picker clearable
                            v-model="formData.calcDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;"
                            placeholder="请选择计量日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
                type="textarea"
                v-model="formData.remark"
                placeholder="请输入备注"
                clearable
                :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件" prop="remark">
            <file-upload v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24"  style="text-align: right;padding-right: 7.5px;margin-top: 18px">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import {addDaliyIntermediate,editDaliyIntermediate} from '@/api/dailyMaintenance/metering/middleApplication'
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
export default {
  name: "index",
  components: {SelectTree,RoadSection},
  data() {
    return {
      formData: {},
      loading: false,
      methodList: [],
      rules: {
        name: [
          {required: true, message: '请输入计量名称', trigger: 'blur'}
        ],
        code: [
          {required: true, message: '请输入计量编号', trigger: 'blur'}
        ],
        domainId: [
          {required: true, message: '请输入管养单位', trigger: 'blur'}
        ],
        maiSecId: [
          {required: true, message: '请输入路段', trigger: 'blur'}
        ],
        calcDomainId: [
          {required: true, message: '请输入申请计量单位', trigger: 'blur'}
        ],
        conId: [
          {required: true, message: '请输入合同', trigger: 'blur'}
        ],
        calcDate: [
          {required: true, message: '请选择计量日期', trigger: 'change'}
        ]
      }
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData = {
            id: val.id,
            calcDomainId: String(val.calcDomainId),
            code: val.code,
            conId: val.conId,
            domainId: String(val.domainId),
            maiSecId: val.maiSecId,
            name: val.name,
            calcDate: val.calcDate,
            remark: val.remark,
            fileId: val.fileId
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
          this.formData.fileId = this.formData.fileId[0]
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        if (this.formData.id) {
          editDaliyIntermediate(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        } else {
          addDaliyIntermediate(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        }
      })
    },
    onClose() {
      this.$emit("close")
    },
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
