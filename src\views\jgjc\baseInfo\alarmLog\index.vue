<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 4 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
            ref="tree"
            :data="filteredTreeData"
            :default-expanded-keys="[1]"
            :expand-on-click-node="false"
            :props="defaultProps"
            highlight-current
            node-key="code"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 20 : 24" :xs="24">
        <!--展开图标-->
        <div v-show="!relaNav" class="rightIcon" @click="relaNav = true">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="'calc(100vh - 260px)'" border size="mini"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
            <el-table-column align="center" fixed="left" type="selection" width="55"></el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {getJCDomainTree, getSensorDeviceTreeNode, getAlertRecordPage, deleteAlertRecord} from "@/api/jgjc/baseInfo/alarmLog";

export default {
  name: "ContentManagement",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: (data) => {
          // 通过该字段判断是否是叶子节点（没有子节点）
          // 这里假设接口返回的节点中，有hasChildren字段标识是否有子节点
          return !data.children
        }
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        {key: 0, field: 'alertMachineCode', label: '传感器编号', visible: true},
        {key: 1, field: 'requestHeaders', label: '请求头', visible: true},
        {key: 2, field: 'requestBody', label: '请求体', visible: true},
        {key: 3, field: 'responseBody', label: '响应体', visible: true},

      ],
      ids: [],
    };
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data;
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      // 如果节点没有子节点且未被加载过
      if (node.level == 4 && !node.childNodes.length && nodeData.structureId && !this.loadedKeys.has(nodeData.structureId)) {
        this.loadChildren(node)
      }
      if (node.level != 5) {
        console.log(node)
        let params = {}
        if (node.level == 1) {
          params.domainName = nodeData.deptName
        } else if (node.level == 2) {
          params.domainName = node.parent.data.deptName
          params.roadName = nodeData.label
        } else if (node.level == 3) {
          params.domainName = node.parent.parent.data.deptName
          params.roadName = node.parent.data.label
          params.structureType = nodeData.structureType
        } else {
          params = nodeData
          // 判断params里面有没有structureId 没有就从父级找 一直找到为止 找到后赋值给params
          let tempNode = node
          while (!tempNode.data.structureId) {
            tempNode = tempNode.parent
          }
          params.structureId = tempNode.data.structureId
        }
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          level: node.level,
          ...params
        }
        this.handleQuery()
      }
    },

    // 加载子节点数据
    async loadChildren(node) {
      const nodeData = node.data
      // 显示加载状态
      node.loading = true
      try {
        const res = await getSensorDeviceTreeNode({structureId: nodeData.structureId})
        if (res.data && res.data.length > 0) {
          // 给树节点添加子节点（Vue.set确保响应式）
          this.$set(nodeData, 'children', res.data)
          // 展开当前节点（可选）
          this.$nextTick(() => {
            node.expanded = true
          })
        }
        // 标记为已加载
        this.loadedKeys.add(nodeData.structureId)
      } finally {
        node.loading = false
      }
    },
    handleQuery() {
      this.loading = true;
      getAlertRecordPage(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        console.error(err);
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: ''
      };
      this.handleQuery();
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id);
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据');
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteAlertRecord({ids: this.ids}).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
  }
};
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
