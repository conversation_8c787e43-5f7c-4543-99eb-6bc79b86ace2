<template>
  <div class="home-container">
    <div class="header">
      <div class="header-logo">
        <img src="@/assets/home/<USER>" />
      </div>
      <div class="home-title">
        <span>公路智慧养护管理平台</span>
      </div>
      <div class="login-user">
        <el-dropdown class="dropdown-menu" trigger="click">
          <div class="user-info">
            <img src="@/assets/home/<USER>" class="user-img" />
            <span>{{ nickName }}</span>
            <i class="el-icon-caret-bottom icon" />
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item divided @click.native="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="video-content">
      <video ref="video" @ended="onEnded('video')" autoplay muted="muted" :src="videoUrl"
        class="banner-inner-video"></video>
    </div>

    <section class="route-list" :class="'route-list-item' + index" v-for="(route, index) in routeList" :key="index"
      @click="handleRouteClick(route, index)">
      <img v-if="index === 0" class="icon"
        :src="routeIndex === index ? require(`@/assets/home/<USER>/platform-active.png`) : routeImgArr[index]" />
      <img v-else :src="routeImgArr[index]" class="icon" />
      <div class="route-title">
        <span :class="routeIndex == index ? 'selected-text' : 'unselected-text'">
          {{ route.text || route.meta.title }}
        </span>
      </div>
    </section>
    <transition name="slide-fade">
      <section class="route-child-list" v-show="childList.length && routeIndex == 0">
        <img src="@/assets/home/<USER>/arrow-down.png" class="arrow-down" alt="" srcset="" />
        <div class="child-list">
          <div v-for="(item, index) in childList" :key="index" class="list-item">
            <img :src="item.icon" class="icon" @click="hanleChildClick(item, index)">
            <span @mouseenter="handleSpanMouseEnter($event)" @mouseleave="handleSpanMouseLeave($event)"
              @click="hanleChildClick(item, index)">{{ item.text }}</span>
          </div>
        </div>
      </section>
    </transition>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import routeTitleBg from '@/assets/home/<USER>/route-title-bg.png'
import routeTitleBgEd from '@/assets/home/<USER>/route-title-bg-ed.png'
import cache from "@/plugins/cache";

export default {
  data() {
    return {
      videoUrl: require(`@/assets/home/<USER>
      routeIndex: null,
      routeList: [
        {
          text: '业务平台',
          children: [
            // {
            //   text: '数据驾驶舱',
            //   icon: require(`@/assets/home/<USER>/cockpit.png`),
            //   path: 'cockpit',
            // },
            {
              text: '日常养护',
              icon: require(`@/assets/home/<USER>/maintenance.png`),
              path: '/dailyMaintenance/construction/ledger',
            },
            {
              text: '计量管理',
              icon: require(`@/assets/home/<USER>/m-m.png`),
              path: '/calculate/metering/settlementView?type=view',
            },
            {
              text: '应急物资',
              icon: require(`@/assets/home/<USER>/e-s.png`),
              path: '/emergencyMaterial/materialDepot',
            },
            {
              text: '养护工程',
              icon: require(`@/assets/home/<USER>/m-e.png`),
              path: '/maintenanceProject/construction/constructionProgress',
            },
            {
              text: '巡检查管理',
              icon: require(`@/assets/home/<USER>/i-m.png`),
              path: '/patrol/statistic/bridgesta',
            },
            {
              text: '路网管理',
              icon: require(`@/assets/home/<USER>/r-n-m.png`),
              path: '/base/road/statistics',
            },
            {
              text: '定期检测',
              icon: require(`@/assets/home/<USER>/p-i.png`),
              path: '/regularTesting/constructionManage/constructionProgress',
            },
            {
              text: '涉路工程',
              icon: require(`@/assets/home/<USER>/r-w.png`),
              path: '',
            },
            {
              text: '基础数据',
              icon: require(`@/assets/home/<USER>/d-b.png`),
              path: '/base/bridge/statistics',
            },
            {
              text: '被损被盗',
              icon: require(`@/assets/home/<USER>/d-s.png`),
              path: '/theft/construction/constructionProgress',
            },
            {
              text: '大件运输',
              icon: require(`@/assets/home/<USER>/b-t.png`),
              path: '/transporation/transportation',
            },
            {
              text: '合同管理',
              icon: require(`@/assets/home/<USER>/c-m.png`),
              path: '/contract/contractInfo',
            },
            // {
            //   text: '预算管理',
            //   icon: require(`@/assets/home/<USER>/r-n-m.png`),
            //   path: '/budgetManage/declaration/advanceView?isshow=true',
            // },
            // {
            //   text: '报表中心',
            //   icon: require(`@/assets/home/<USER>/r-n-m.png`),
            //   path: '/tongjibaobiao/baseData/ancillary',
            // },
          ],
        },
        {
          text: '科学决策',
          path: '',
        },
        {
          text: '健康监测',
          path: 'onemap?type=1',
        },
        {
          text: '基础数据库',
          path: '/cockpit',
        },
        {
          text: '养护一张图',
          path: 'onemap',
        },
      ],  // 菜单数据
      childList: [], // 选中菜单下的子菜单
      childIndex: null, // 选中的下标
      scrollLeft: 0,
      routeImgArr: [
        require(`@/assets/home/<USER>/platform.png`),
        require(`@/assets/home/<USER>/decision.png`),
        require(`@/assets/home/<USER>/monitor.png`),
        require(`@/assets/home/<USER>/base-data.png`),
        require(`@/assets/home/<USER>/one-map.png`),
      ], //
      routeTitleBg,
      routeTitleBgEd,
    };
  },
  computed: {
    ...mapGetters(["sidebarRouters", 'nickName']),
    userName() {
      return this.$store.getters.name || 'admin1';
    }
  },
  mounted() {
    cache.session.remove('from');
    // this.routeList = this.sidebarRouters.filter(v => !v.hidden && v.path != '')
    // 控制显示 5 个样式
    // this.routeList.length = 5;
    this.childList = this.routeList[0].children || [];
    this.playVideo();
  },
  methods: {
    // 播放video 相关
    onEnded(e) {
      // 第二个视频开始播放 即第一个视频播放完成后 开始播放第二个视频；
      // 第二个视频播放完毕时 调用该方法 再次播放 实现循环效果
      this.$refs.video?.play();
    },
    playVideo() {
      this.$refs.video?.play(); // 第一个video 播放
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => { });
    },
    // 修改滚动条位置
    handleScroll(t) {
      let scrollWidth = this.$refs.scrollRef.scrollWidth
      if (t == 'pre') {
        if (this.scrollLeft <= 100) {
          this.scrollLeft = 0
        } else {
          this.scrollLeft -= scrollWidth / this.childList.length
        }
      } else {
        if (this.scrollLeft >= scrollWidth / 2) {
          this.scrollLeft = scrollWidth / 2
        } else {
          this.scrollLeft += scrollWidth / this.childList.length
        }
      }
      this.$refs.scrollRef.scrollLeft = this.scrollLeft;
    },
    // 点击子菜单
    hanleChildClick(item, index) {
      this.childIndex = index
      // TODO：路由跳转
      let routeUrl;
      // if (item.children) {
      //   routeUrl = this.routeList[this.routeIndex].path + '/' + item.path + '/' + item.children[0].path
      // }else {
      //   routeUrl = this.routeList[this.routeIndex].path + '/' + item.path
      // }
      // this.$router.push({ path: routeUrl || "/" }).catch(() => { });
      if (item.path) {
        let str = 'home'
        let encodedStr = btoa(str);
        cache.session.setJSON("from", encodedStr);
        // if (item.path.includes('?')) {
        //   item.path = item.path + '&from=' + encodedStr
        // } else {
        //   item.path = item.path + '?from=' + encodedStr
        // }
        this.$router.push({ path: item.path || "/" }).catch(() => { });
      }
    },
    handleRouteClick(item, index) {
      if (index === 0) {
        this.routeIndex = this.routeIndex === index ? null : index;
      }
      if (this.routeIndex === 0) {
        this.childList = item.children
        this.childIndex = null
      } else {
        this.childList = []
      }
      if (item.path) {
        this.$router.push({ path: item.path || "/" }).catch(() => { });
      }
    },
    handleSpanMouseEnter(event) {
      const icon = event.target.previousElementSibling;
      if (icon) {
        icon.style.transform = 'scale(1.1)';
      }
    },
    handleSpanMouseLeave(event) {
      const icon = event.target.previousElementSibling;
      if (icon) {
        icon.style.transform = 'scale(1)';
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

$sw: 300px;
$h: 160px;

html,
body,
* {
  margin: 0;
  padding: 0;
}

.home-container {
  user-select: none;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  min-width: 800px;

  .header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: vwpx($h);
    background-image: url('~@/assets/home/<USER>');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-logo {
      width: vwpx($sw);
      height: vwpx($h);

      display: flex;
      align-items: center;

      img {
        // width: vwpx(300px);
        // height: vwpx(97px);
        width: auto;
        height: 65%;
        margin-left: vwpx(20px);
        margin-top: vwpx(-40px);
      }
    }

    .home-title {
      flex: 1;
      display: flex;
      justify-content: center;

      span {
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        // font-size: vwpx(76px);
        font-size: 2vw;
        letter-spacing: vwpx(12px);
        text-shadow: 0px 0px vwpx(100px) rgba(16, 94, 217, 0.6);
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 50%, #20A9FF 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        margin-top: vwpx(-12px);
      }
    }

    .login-user {
      width: vwpx($sw);
      height: vwpx($h);

      display: flex;
      align-items: center;

      .user-img {
        width: vwpx(48px);
        height: vwpx(48px);
        margin-right: vwpx(20px);
      }

      .dropdown-menu {
        cursor: pointer;
        z-index: 99;

        .user-info {
          font-weight: 400;
          font-size: vwpx(32px);
          color: #00FDFD;
          text-align: center;
          font-style: normal;
          text-transform: none;

          display: flex;
          align-items: center;

          .icon {
            color: #BBBBBB;
            margin-left: vwpx(10px);
          }
        }
      }
    }
  }

  .video-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .banner-inner-video {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }
  }

  .route-list {
    position: absolute;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .route-title {
      width: vwpx(460px);
      height: vwpx(96px);

      // background-image: url('~@/assets/home/<USER>/route-title-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-bottom: vwpx(20px);

      display: flex;
      align-items: center;
      justify-content: center;

      .selected-text {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vwpx(60px);
        text-shadow: 0px 0px 10px #0066FF;
        text-align: center;
        font-style: normal;
        text-transform: none;
        // background: linear-gradient(0deg, #FEB035 0%, #FFFFFF 100%);
        // background-clip: text;
        // -webkit-background-clip: text;
        color: #00FDFD;
        letter-spacing: vwpx(2px);
      }

      .unselected-text {
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vwpx(60px);
        color: #FFFFFF;
        text-shadow: 0px 0px 10px #0066FF;
        text-align: center;
        font-style: normal;
        text-transform: none;
        letter-spacing: vwpx(2px);
      }
    }

    .icon {
      width: vwpx(370px);
      height: vwpx(370px);
      color: #ffffff;
      // 添加过渡效果，使放大更平滑
      transition: transform 0.3s ease;
    }

    // 鼠标悬停时放大
    .icon:hover {
      transform: scale(1.05);
    }

    transform: translate(-50%, -50%);
  }

  .route-list-item0 {
    top: 88%;
    left: 50%;
    // transform: translate(-50%, -50%) scale(1) !important;
  }

  .route-list-item1 {
    top: 70%;
    left: 82%;
  }

  .route-list-item2 {
    top: 40%;
    left: 82%;
  }

  .route-list-item3 {
    top: 40%;
    left: 19%;
  }

  .route-list-item4 {
    top: 70%;
    left: 19%;
  }

  .slide-fade-enter-active,
  .slide-fade-leave-active {
    transition: all 0.3s ease;
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(40px) !important;
  }

  .slide-fade-enter {
    transform: translate(-50%, -50%) translateY(40px) !important;
  }

  .route-child-list {
    position: absolute;
    left: 50%;
    bottom: vwpx(210px);
    transform: translate(-50%, -50%);
    width: vwpx(1228px);
    height: vwpx(716px);
    // overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: rgba(0, 166, 255, 1) 1px solid;
    border-radius: 10px;
    background: rgba(4, 14, 46, 0.90);
    box-shadow: inset 0px 0px 30px 0px rgba(0, 101, 255, 0.80);

    .arrow-down {
      position: absolute;
      bottom: -36px;
      left: 46%;
    }

    .child-list {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;
      align-content: center;
      margin: 0 vwpx(20px);

      .list-item {
        flex-basis: calc(100% / 3);
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin: vwpx(20px) 0;
        padding-left: vwpx(60px);

        .icon {
          width: vwpx(120px);
          height: vwpx(120px);
          transition: transform 0.3s ease;
          cursor: pointer;
        }

        .icon:hover {
          transform: scale(1.1) !important;
        }

        span {
          margin-left: vwpx(20px);
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 700;
          font-size: vwpx(32px);
          color: #FFFFFF;
          line-height: 21px;
          text-shadow: 0px 0px 10px #0066FF;
          text-align: left;
          font-style: normal;
          text-transform: none;
          cursor: pointer;
        }
      }
    }
  }
}

@keyframes click {
  0% {
    transform: scale(1);
  }

  20% {
    transform: scale(0.8);
  }

  40% {
    transform: scale(0.5);
  }

  60% {
    transform: scale(0.8);
  }

  100% {
    transform: scale(1);
  }
}

// ::-webkit-scrollbar {
//   width: 5px;
//   height: 5px;
//   background-color: #ffffff;
//   opacity: 0;
// }

// ::-webkit-scrollbar-track {
//   border-radius: 10px;
//   background-color: #ffffff;
// }

// ::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   background-color: #ffffff;
// }
::v-deep {
  .el-dropdown-menu__item {
    margin: 0 !important;
    border: 0 !important;
  }

  .el-dropdown-menu__item--divided:before {
    height: 1px !important;
  }
}
</style>
