<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
<!--        <el-col :span="12">-->
<!--          <el-form-item label="验收人员" prop="visaBy">-->
<!--            <el-input v-model="formData.visaBy" placeholder="请输入验收人员" clearable>-->
<!--            </el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="审核人" prop="reviewNo">-->
<!--            <el-input v-model="formData.reviewNo" placeholder="请输入验收人员" clearable>-->
<!--            </el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="费用类型" prop="costType">-->
<!--            <dict-select type="cost_name" clearable-->
<!--                         v-model="formData.costType" placeholder="请选择是否计量"-->
<!--                         style="width: 100%"></dict-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="是否计量" prop="canMath">-->
<!--            <dict-select type="bridge_simple_bool" clearable-->
<!--                         v-model="formData.canMath" placeholder="请选择是否计量"-->
<!--                         style="width: 100%"></dict-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="24" style="margin-top: 18px">
          <el-table v-adjust-table
              :data="formData.settleMethodList"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号"
                >
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称"
                >
              <template slot-scope="scope">
                <el-input v-model="scope.row.schemeName">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.unit">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价"
                >
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式"
                >
              <template slot-scope="scope">
                <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量"
                >
              <template slot-scope="scope">
                <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金"
                >
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注"
                >
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark">
                </el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">
            <div>计算式及说明</div>
          </div>
          <el-input v-model="formData.calcDesc" type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="text-align: right;padding-right: 7.5px;margin-top: 18px">
          <el-button type="primary" @click="onSave">确 定</el-button>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import {getTreeData} from "@/api/contract/quotationSystem"
import {updateOtherMethodList} from "@/api/dailyMaintenance/metering/addPrice";
import { Decimal } from 'decimal.js';

export default {
  name: "index",
  data() {
    return {
      formData: {},
      loading: false,
      urgentDegreeOptions: [{
        "label": "计算",
        "value": 1
      }, {
        "label": "不计算",
        "value": 2
      }]
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val) {
          this.formData = {
            id: val.settleId,
            settleMethodList: val.methodList,
            calcDesc: val.calcDesc
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    onSave() {
      this.loading = true
      updateOtherMethodList(this.formData).then(res => {
        this.loading = false
        this.$message.success('保存成功')
        this.onClose()
      })
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      this.total = await this.formData.settleMethodList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
    },
    onClose() {
      this.$emit("close")
    },
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
