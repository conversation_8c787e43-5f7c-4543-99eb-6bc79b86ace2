<template>
  <div class="road-interflow-edit">
    <el-row>
      <el-row>
        <el-col :span="24">
          <el-form
            ref="queryForm"
            :inline="true"
            :model="queryParams"
            size="mini"
          >
            <el-form-item label="物资类型">
              <el-select v-model="queryParams.typeList" multiple style="width: 100px">
                <el-option v-for="item in typeList" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="物资名称">
              <el-input
                style="width: 100px"
                v-model="queryParams.assetsName"
              ></el-input>
            </el-form-item>
            <el-form-item label="物资编码">
              <el-input
                style="width: 100px"
                v-model="queryParams.assetsCode"
              ></el-input>
            </el-form-item>
            <el-form-item label="是否为标准物资">
              <el-select
            v-model="queryParams.isStandard"
            placeholder="是否为标准物资"
            clearable
            style="width: 100px"
          >
            <el-option
              v-for="item in standardOptions"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                size="mini"
                type="primary"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8" v-if="hasHandle">
        <el-col :span="1.5">
          <el-button
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="openDetailDialog(null)"
            >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            icon="el-icon-download"
            size="mini"
            type="warning"
            @click="exportList"
            >导出
          </el-button>
        </el-col>
<!--        <el-col :span="1.5">-->
<!--          <el-button icon="el-icon-view" size="mini" type="primary" @click="handleOpenOperate"-->
<!--            >操作记录-->
<!--          </el-button>-->
<!--        </el-col>-->
      </el-row>
      <el-col :span="24">
        <div class="draggable">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            border
            height="500"
            highlight-current-row
            row-key="id"
            size="mini"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @expand-change="loadData"
            @row-click="rowClick"
          >
            <el-table-column type="expand" v-if="hasHandle">
              <template slot-scope="props">
                <el-table v-adjust-table
                  :data="props.row.methodList"
                  style="width: 100%"
                  v-loading="methodLoading"
                >
                  <el-table-column
                    type="index"
                    align="center"
                    label="序号"
                    width="68"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="type"
                    align="center"
                    label="类型"
                  >
                    <template slot-scope="scope">
                      {{ methodType[scope.row.type] }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="assetsName"
                    align="center"
                    label="物资名称"
                  >
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="单位">
                  </el-table-column>
                  <el-table-column prop="quantity" align="center" label="数量">
                  </el-table-column>
                  <el-table-column prop="occurrenceTime" align="center" label="发生时间">
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="props.row.childTotal > 0"
                  :limit.sync="props.row.queryParams.pageSize"
                  :page.sync="props.row.queryParams.pageNum"
                  :total="props.row.childTotal"
                  @pagination="loadData(props.row)"
                />
              </template>
            </el-table-column>
            <el-table-column type="selection" width="55" v-else></el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="68"
              align="center"
            ></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              label="操作"
              fixed="right"
              width="120"
              align="center"
              v-if="hasHandle"
            >
              <template slot-scope="props">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="openDetailDialog(props.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(props.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="操作记录"
      :visible.sync="openOperateInfo"
      size="50%"
      destroy-on-close
      v-if="openOperateInfo"
      append-to-body
      modal-append-to-body
    >
      <EventTreeInfo :handleList="handleList"></EventTreeInfo>
    </el-dialog>
    <el-dialog
      :title="detailDialogTitle"
      :visible.sync="detailDialog"
      width="400px"
      v-if="detailDialog"
      append-to-body
      modal-append-to-body
    >
      <el-form :model="curMaterialForm" label-width="100px">
        <el-form-item label="物资名称">
          <el-row>
            <el-col :span="24">
              <el-button
                style="width: 100%; height: 28px; text-align: left"
                @click="openMaterialListDialog"
              >{{ curMaterialForm.assetsName }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="库存数量">
          <el-row>
            <el-col :span="24">
              <el-input v-model="curMaterialForm.stock" />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="saveDetailChange"
          >保存</el-button
        >
        <el-button size="mini" @click="closeDetailDialog">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="选择物资"
      :visible.sync="materialListDialog"
      width="1000px"
      v-if="materialListDialog"
      append-to-body
      modal-append-to-body
    >
      <el-row>
        <el-col :span="24">
          <el-form
            :inline="true"
            :model="materialListParams"
            size="mini"
          >
            <el-form-item label="物资类型">
              <el-select v-model="materialListParams.type" style="width: 100px">
                <el-option v-for="item in typeList" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="物资名称">
              <el-input
                style="width: 100px"
                v-model="materialListParams.name"
              ></el-input>
            </el-form-item>
            <el-form-item label="物资编码">
              <el-input
                style="width: 100px"
                v-model="materialListParams.code"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                size="mini"
                type="primary"
                @click="handleQueryList"
                >搜索</el-button
              >
              <el-button
                icon="el-icon-refresh"
                size="mini"
                @click="resetListQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="24">
          <div class="draggable">
            <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="materialList"
              border
              height="300"
              highlight-current-row
              row-key="id"
              size="mini"
              stripe
              style="width: 100%"
              @row-click="materialClick"
            >
              <el-table-column
                type="index"
                label="序号"
                width="68"
                align="center"
              ></el-table-column>
              <template v-for="(column, index) in materialListColumns">
                <el-table-column
                  v-if="column.visible"
                  :label="column.label"
                  :prop="column.field"
                  :width="column.width"
                  align="center"
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :index="index"
                        :render="column.render"
                        :row="scope.row"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
              v-show="materialListTotal > 0"
              :limit.sync="materialListParams.pageSize"
              :page.sync="materialListParams.pageNum"
              :total="materialListTotal"
              @pagination="handleQueryList"
            />
          </div>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="confirmSelectMaterial"
          >确 定</el-button
        >
        <el-button size="mini" @click="closeMaterialDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import EventTreeInfo from "../component/eventTreeInfo.vue"
import { getMaterialTypeTree, getMaterialList } from "@/api/emergencyMaterial/materialType";
import { getMaterialDetailList, addMaterialDetail, editMaterialDetail, delMaterialDetail,materialDetailListEvent, getDetailNodeInfo } from "@/api/emergencyMaterial/materialDepot"
export default {
  components: {
    EventTreeInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: [],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData: [],
      total: 0,
      columns: [
        {
          key: 1,
          field: "typeName",
          label: `物资类型`,
          visible: true,
        },
        {
          key: 2,
          field: "assetsCode",
          label: `物资编码`,
          visible: true,
        },
        {
          key: 3,
          field: "assetsName",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 4,
          field: "model",
          label: `规格型号`,
          visible: true,
        },
        {
          key: 5,
          field: "unit",
          label: `单位`,
          visible: true,
        },
        {
          key: 6,
          field: "quantity",
          label: `标配数量`,
          visible: true,
        },
        {
          key: 7,
          field: "stock",
          label: `库存数量`,
          visible: true,
        },
      ],
      detailDialogTitle: "新增物资",
      detailDialog: false,
      curMaterialForm: null,
      materialListDialog: false,
      materialList: [],
      materialListParams: {
        pageNum: 1,
        pageSize: 10,
      },
      materialListTotal: 0,
      materialListColumns: [
        {
          key: 1,
          field: "code",
          label: `物资编码`,
          visible: true,
        },
        {
          key: 2,
          field: "name",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 3,
          field: "model",
          label: `规格型号`,
          visible: true,
        },
        {
          key: 4,
          field: "unit",
          label: `单位`,
          visible: true,
        },
        {
          key: 5,
          field: "quantity",
          label: `标配数量`,
          visible: true,
        },
        {
          key: 6,
          field: "repairCycle",
          label: `维护周期(月)`,
          visible: true,
        },
        {
          key: 7,
          field: "isStandard",
          label: `是否标准物资`,
          visible: true,
          slots: true,
          render: (row, index)=> {
            return (
              <span>{row.isStandard == 1 ? '是':'否'}</span>
            )
          }
        },
        {
          key: 8,
          field: "remark",
          label: `备注`,
          visible: true,
        },
      ],
      curSelectedMaterial: null,
      typeList: [],
      standardOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      methodType: {
        1:"出库",
        2:"入库",
        3:"入库补充",
        4:"核销",
        5:"核销补充",
        6:"盘点",
        7:"维修保养"
      },
      openOperateInfo: false,
      handleList: [],
      curRow: null
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {},
    },
    hasHandle: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.handleQuery();
    this.getTypeList()
  },
  methods: {
    getTypeList() {
      getMaterialTypeTree().then((res) => {
        this.typeList = res.data?.children || [];
      });
    },
    handleQuery() {
      this.loading = true;
      this.queryParams.materialId = this.rowData.id;
      if (this.queryParams.isStandard === '') {
        delete this.queryParams.isStandard
      }
      getMaterialDetailList(this.queryParams).then((res) => {
        this.tableData = res.rows
        this.tableData.forEach((item) => {
          item.queryParams = {
            pageNum: 1,
            pageSize: 10,
            materialId: this.rowData.id,
            assetsId: item.assetsId
          };
          item.childTotal = 0;
        });
        this.total = res.total;
        this.loading = false;
      });
    },
    handleSelectionChange(rows) {
      if(!this.hasHandle) {
        this.$emit('depotInfoSelect',rows)
      }
    },
    exportList() {
      this.download(
        "manager/emergency/material/detail/export",
        { ...this.queryParams },
        `物资库详情_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    openDetailDialog(row) {
      if (!row) {
        this.detailDialogTitle = '编辑物资'
      }
      this.curMaterialForm = row || {};
      this.detailDialog = true;
    },
    closeDetailDialog() {
      this.detailDialog = false;
      this.curMaterialForm = null;
    },
    saveDetailChange() {
      if (this.curMaterialForm.id) {
        editMaterialDetail(this.curMaterialForm).then(()=> {
          this.closeDetailDialog();
          this.handleQuery()
        })
      }else {
        addMaterialDetail(this.curMaterialForm).then(()=> {
          this.closeDetailDialog();
          this.handleQuery()
        })
      }
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        delMaterialDetail(row.id).then(()=> {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      });
    },
    openMaterialListDialog() {
      this.materialListParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.handleQueryList()
      this.materialListDialog = true
    },
    handleQueryList() {
      this.loading = true;
      getMaterialList(this.materialListParams)
        .then((res) => {
          this.materialList = res.rows || [];
          this.materialListTotal = res.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    materialClick(row) {
      this.curSelectedMaterial = row
    },
    confirmSelectMaterial() {
      if (!this.curSelectedMaterial) {
        this.$message.warning('请选择物资')
        return
      }
      this.curMaterialForm = {
        id: this.curMaterialForm.id,
        materialId: this.rowData.id,
        stock: ''
      }
      this.curMaterialForm.assetsName = this.curSelectedMaterial.name
      this.curMaterialForm.assetsId = this.curSelectedMaterial.id
      let stock = this.curSelectedMaterial.quantity
      this.curMaterialForm.stock = stock

      this.closeMaterialDialog()
      this.$forceUpdate()
    },
    closeMaterialDialog() {
      this.curSelectedMaterial = null
      this.materialListDialog = false
    },
    loadData(row) {
      this.methodLoading = true;
      materialDetailListEvent(row.queryParams).then((res) => {
        this.$set(row, "methodList", res.data);
        this.$set(row, "childTotal", res.total);
        this.methodLoading = false;
      });
    },
    rowClick(row) {
      this.curRow = row
    },
    handleOpenOperate() {
      if (!this.curRow || !this.curRow.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      materialDetailListEvent({
        materialId: this.rowData.id,
        assetsId: this.curRow.assetsId
      }).then(res=> {
        console.log(res)
        this.handleList = res.data || []
        this.openOperateInfo = true;
      })
    },
    resetListQuery() {
      this.materialListParams = {
        pageNum: 1,
        pageSize: 10,
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        isStandard: ''
      };
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
