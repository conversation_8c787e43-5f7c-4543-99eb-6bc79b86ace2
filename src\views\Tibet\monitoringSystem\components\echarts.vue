<template>
  <div class="e-charts" :style="{ height: height }"></div>
</template>

<script>
import * as echarts from "echarts";
// require("echarts/theme/macarons"); // echarts theme

export default {
  props: {
    height: {
      type: String,
      default: "260px",
    },
    option: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    reload() {
      this.chart.resize();
      this.$forceUpdate();
    },
    initChart() {
      this.chart = echarts.init(this.$el, "macarons", { renderer: "svg" });
      this.chart.setOption(this.option);
      // 监听窗口变化，重新渲染图表

      window.addEventListener("reload", () => {
        this.chart.setOption(this.option);
      })

      window.addEventListener("resize", () => {

        this.chart.resize();
        this.$forceUpdate();
      });
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.initChart);
    window.removeEventListener("reload", this.initChart);
  }
};
</script>

<style lang="scss" scoped>
.e-charts {
  width: 100%;
}
</style>
