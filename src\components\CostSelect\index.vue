<template>
  <div>
    <el-select v-model="selectValue" :placeholder="placeholder" clearable style="width: 100%;"
               :disabled="readOnly" @change="changeType">
      <el-option v-for="(item,index) in typeList"
                 :key="index"
                 :label="item.typeName"
                 :value="item.id">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import {getCostType, getCostType2} from "@/api/budgetManage/typeConfig";

export default {
  name: 'CostSelect',
  props: {
    value: {
      type: Object,
      default: ''
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    },
    placeholder: {
      type: String,
      default: '费用类型'
    },
    apiType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      typeList: [],
    }
  },
  computed: {
    selectValue: {
      get: function () {
        return String(this.value || '')
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  mounted() {
    this.getType()
  },
  watch: {
    type: {
      handler(val) {
        if (val) {
          this.getType()
        }
      }
    }
  },
  methods: {
    getType() {
      if (this.apiType == 1) {
        getCostType(this.type).then(res => {
          this.typeList = res.data
        })
      } else {
        getCostType2(this.type).then(res => {
          this.typeList = res.data
        })
      }
    },
    changeType(e) {
      this.$emit('change', e)
    }
  }
}
</script>
<style scoped lang="scss">

</style>
