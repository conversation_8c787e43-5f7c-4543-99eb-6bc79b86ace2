<template>
  <div class="timeline-container">
    <div class="timeline-bar-wrapper">
      <div class="timeline-bar" ref="timelineBar" @mousedown="onBarMouseDown" @touchstart="onBarTouchStart"
        :style="{ transform: `translateX(${-scrollLeft}px)` }" @wheel="onWheel" >
        <div class="timeline-thumb" ref="thumb" :style="{ left: thumbLeft + 'px' }" @mousedown.stop="onThumbMouseDown"
          @touchstart.stop="onThumbTouchStart">
          <svg width="12" height="12" viewBox="0 0 18 18">
            <polygon points="9,0 18,18 0,18" fill="rgba(0,253,253,0.6)" />
          </svg>
        </div>
        <div v-for="(item, idx) in visibleSegments" :key="item.time + ('' + idx) + visibleSegments.length"
          class="timeline-segment" :style="{ left: getSegmentLeft(idx) + 'px' }"></div>
      </div>
    </div>
    <div class="timeline-labels-wrapper" ref="labelsWrapper">
      <div class="timeline-labels" :style="{ transform: `translateX(${-scrollLeft}px)` }" @wheel="onWheel"
        @mousedown="onLabelsMouseDown" @touchstart="onLabelsTouchStart">
        <div v-for="(item, idx) in visibleSegments" :key="item.time + ('' + idx) + visibleSegments.length"
          class="timeline-label" :class="{ active: idx === currentIndex }" :style="{ width: labelWidth + 'px' }"
          @click="onLabelClick(idx)">
          {{ item.time }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const SEGMENT_WIDTH = 35; // px，每个时间段的宽度
const BAR_PADDING = 12; // px, left/right padding for bar

export default {
  name: "TimeLine",
  props: {
    // 传入时间段数据，格式: [{ time: "10:00" }, ...]
    timeList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    value: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      currentIndex: 0,
      dragging: false,
      dragType: null, // 'thumb' or 'bar' or 'labels'
      dragStartX: 0,
      dragStartLeft: 0,
      scrollLeft: 0,
      labelWidth: SEGMENT_WIDTH,
      barWidth: 0,
      visibleCount: 0
    };
  },
  computed: {
    thumbLeft() {
      // thumb 的 left 相对 bar
      return BAR_PADDING + this.currentIndex * this.segmentGap;
    },
    segmentGap() {
      // 间隔
      // if (this.visibleCount > 1) {
      //   return (this.barWidth - BAR_PADDING * 2) / (this.visibleCount - 1);
      // }
      return SEGMENT_WIDTH;
    },
    visibleSegments() {
      // 当前可见的 segments
      let a = this.timeList.map(item => {
        // 去除字符串中的 + 号，提取时间部分
        const time = item.replace(/\+/g, '');
        // 匹配 + 号数量
        const plusMatch = item.match(/\+/g);
        const day = plusMatch ? plusMatch.length : 0;
        return { time, day };
      });
      return this.timeList.map(item => {
        // 去除字符串中的 + 号，提取时间部分
        const time = item.replace(/\+/g, '');
        // 匹配 + 号数量
        const plusMatch = item.match(/\+/g);
        const day = plusMatch ? plusMatch.length : 0;
        return { time, day };
      });
    }
  },
  watch: {
    value(val) {
      let index = this.timeList.findIndex(item => item === val);
      this.currentIndex = index;
      this.scrollToIndex(index);
    },
    currentIndex(val) {
      this.$emit("input", this.timeList[val]);
      this.scrollToIndex(val);
    },
    timeList: {
      handler() {
        this.$nextTick(this.updateBar);
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.updateBar();
    window.addEventListener("mousemove", this.onMouseMove);
    window.addEventListener("mouseup", this.onMouseUp);
    window.addEventListener("touchmove", this.onTouchMove, { passive: false });
    window.addEventListener("touchend", this.onTouchEnd);
  },
  beforeDestroy() {
    window.removeEventListener("mousemove", this.onMouseMove);
    window.removeEventListener("mouseup", this.onMouseUp);
    window.removeEventListener("touchmove", this.onTouchMove);
    window.removeEventListener("touchend", this.onTouchEnd);
  },
  methods: {
    updateBar() {
      // 计算 bar 宽度和可见数量
      this.$nextTick(() => {
        // 获取 bar 的实际宽度
        this.barWidth = this.$refs.timelineBar ? this.$refs.timelineBar.offsetWidth : 0;
        // 可见的 segment 数量
        this.visibleCount = this.timeList.length;
        // 更新 thumb 位置
        this.$forceUpdate();
        // 重新计算 thumb 的位置
        this.$nextTick(() => {
          if (this.$refs.thumb && this.currentIndex != null) {
            const left = BAR_PADDING + this.currentIndex * SEGMENT_WIDTH;
            this.$refs.thumb.style.left = left + "px";
          }
        });
        this.$nextTick(() => {
          const timeLineLabels = this.$refs.labelsWrapper.querySelectorAll(".timeline-label");
          let allLabelWidth = null;
          timeLineLabels.forEach((label) => {
            const width = label.getBoundingClientRect().width;
            allLabelWidth += width;
          });
          this.$refs.timelineBar.style.width = allLabelWidth + 'px';
          this.scrollLeft = 0
          this.currentIndex = 0
        })
      });
    },

    getSegmentLeft(idx) {
      return idx * this.segmentGap;
    },

    scrollToIndex(index) {
      if (this.$refs.labelsWrapper) {
        const targetLeft = index * this.labelWidth;
        const wrapperWidth = this.$refs.labelsWrapper.offsetWidth;
        const maxScroll = this.timeList.length * this.labelWidth - wrapperWidth;

        this.scrollLeft = Math.max(0, Math.min(targetLeft - wrapperWidth / 2, maxScroll));
      }
    },

    onLabelClick(index) {
      this.currentIndex = index;
    },

    onBarMouseDown(e) {
      this.startDrag('bar', e.clientX);
    },

    onBarTouchStart(e) {
      if (e.touches.length === 1) {
        this.startDrag('bar', e.touches[0].clientX);
      }
    },

    onThumbMouseDown(e) {
      this.startDrag('thumb', e.clientX);
    },

    onThumbTouchStart(e) {
      if (e.touches.length === 1) {
        this.startDrag('thumb', e.touches[0].clientX);
      }
    },

    onLabelsMouseDown(e) {
      this.startDrag('labels', e.clientX);
    },

    onLabelsTouchStart(e) {
      if (e.touches.length === 1) {
        this.startDrag('labels', e.touches[0].clientX);
      }
    },

    startDrag(type, clientX) {
      this.dragging = true;
      this.dragType = type;
      this.dragStartX = clientX;

      if (type === 'thumb') {
        this.dragStartLeft = this.thumbLeft;
      } else if (type === 'labels') {
        this.dragStartLeft = this.scrollLeft;
      }
    },

    onMouseMove(e) {
      if (!this.dragging) return;
      e.preventDefault();
      this.handleDrag(e.clientX);
    },

    onTouchMove(e) {
      if (!this.dragging || e.touches.length !== 1) return;
      e.preventDefault();
      this.handleDrag(e.touches[0].clientX);
    },

    handleDrag(clientX) {
      const deltaX = clientX - this.dragStartX;

      if (this.dragType === 'thumb') {
        // 拖动 thumb
        const newLeft = this.dragStartLeft + deltaX;
        const maxLeft = this.barWidth - BAR_PADDING * 2;
        const clampedLeft = Math.max(0, Math.min(newLeft, maxLeft));

        // 计算对应的索引
        if (this.visibleCount > 1) {
          this.currentIndex = Math.round((clampedLeft / maxLeft) * (this.visibleCount - 1));
        }
      } else if (this.dragType === 'labels') {
        // 拖动标签区域
        const newScrollLeft = this.dragStartLeft - deltaX;
        const maxScroll = this.timeList.length * this.labelWidth - this.$refs.labelsWrapper.offsetWidth;
        this.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScroll));
      } else if (this.dragType === 'bar') {
        // 点击 bar 直接跳转
        const rect = this.$refs.timelineBar.getBoundingClientRect();
        const relativeX = clientX - rect.left - BAR_PADDING;
        const maxLeft = this.barWidth - BAR_PADDING * 2;

        if (this.visibleCount > 1 && relativeX >= 0 && relativeX <= maxLeft) {
          this.currentIndex = Math.round((relativeX / maxLeft) * (this.visibleCount - 1));
        }
      }
    },

    onMouseUp() {
      this.dragging = false;
      this.dragType = null;
    },

    onTouchEnd() {
      this.dragging = false;
      this.dragType = null;
    },

    onWheel(e) {
      e.preventDefault();
      const scrollSensitivity = 0.6; // 可调整此值控制滚动灵敏度，值越小速度越慢
      const delta = e.deltaY > 0 ? 1 : -1;
      const indexChange = Math.round(delta * scrollSensitivity); // 计算索引变化量
      const newIndex = Math.max(0, Math.min(this.currentIndex + indexChange, this.timeList.length - 1));
      this.currentIndex = newIndex;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.timeline-container {
  width: 100%;
  user-select: none;
}

.timeline-bar-wrapper {
  position: relative;
  height: vwpx(30px);
}

.timeline-bar {
  position: relative;
  height: vwpx(6px);
  background: #00FDFD;
  border-radius: vwpx(3px);
  cursor: pointer;
}

.timeline-thumb {
  position: absolute;
  top: -4px;
  width: vwpx(20px);
  height: vwpx(20px);
  cursor: grab;
  z-index: 10;
}

.timeline-thumb:active {
  cursor: grabbing;
}

.timeline-segment {
  position: absolute;
  top: 0;
  width: vwpx(2px);
  height: 100%;
  background: #999;
  border-radius: 1px;
}

.timeline-labels-wrapper {
  overflow: hidden;
  height: vwpx(50px);
}

.timeline-labels {
  display: flex;
  transition: transform 0.1s ease;
}

.timeline-label {
  flex-shrink: 0;
  text-align: center;
  font-size: vwpx(24px);
  color: #fff;
  cursor: pointer;
  padding: 5px 0;
  transition: color 0.2s ease;
}

.timeline-label:hover {
  color: #409eff;
}

.timeline-label.active {
  color: #409eff;
  font-weight: bold;
}
</style>
