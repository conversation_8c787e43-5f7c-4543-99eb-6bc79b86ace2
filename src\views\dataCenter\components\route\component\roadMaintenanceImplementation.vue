<template>
  <div class="road-maintenance-implementation" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" key="implementationKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
// api
import { getRoadMaintenanceImplementation } from '@/api/cockpit/route';

export default {
  name: 'RoadMaintenanceExpenditure',
  components: {
    Echarts
  },
  data() {
    return {
      option: null,
      loading: false,
    }
  },
  created() { },
  async mounted() {
    this.loading = true;
    const data = await this.getData();
    this.loading = false;
    if (!data) return;
    let xAxisData = [];
    let seriesData = [];
    let unit = '个';
    xAxisData = data.map(v => v.name)
    seriesData = data.map(v => v.count || v.sumFund)
    seriesData = seriesData.map(value => (value / 10000))
    unit = data.find((v) => v.count) ? '个' : '万元'
    this.option = this.initCharts(unit, xAxisData, seriesData);
  },
  methods: {
    getData() {
      return new Promise((resolve, reject) => {
        getRoadMaintenanceImplementation().then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            reject(res);
          }
        }).catch(err => {
          reject(err);
        });
      })
    },
    initCharts(unit = '个', xAxisData = [], data = []) {
      let option = {
        backgroundColor: 'rgba(255,255,255,0)',
        grid: {
          top: '10%',
          right: '2%',
          left: '10%',
          bottom: '7%'
        },
        xAxis: [{
          type: 'category',
          color: '#59588D',
          data: xAxisData || ['预防养护', '修复养护', '专项养护', '应急养护'],
          axisLine: {
            lineStyle: {
              color: 'transparent'
            }
          },
          axisLabel: {
            color: '#ffffff',
            textStyle: {
              fontSize: 24
            },
          },
        }],
        yAxis: [{
          name: unit,
          nameTextStyle: {
            color: '#999999',
            fontSize: 24,
            padding: [0, 40, 20, -0]
          },
          axisLabel: {
            formatter: '{value}',
            color: '#999999',
            textStyle: {
              fontSize: 24,
            }
          },
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110,112,121,0.5)'
            }
          },
          splitArea: false,
          min: 0,
        }],
        series: [{
          type: 'bar',
          data: data || [600, 1420, 1180, 590],
          barWidth: '20px',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#03F2FE' // 0% 处的颜色
              }, {
                offset: 1,
                color: '#0057AC' // 100% 处的颜色
              }], false),
              barBorderRadius: [30, 30, 0, 0],
            }
          },
        }]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
.road-maintenance-implementation {
  width: 100%;
  height: 100%;
  padding: 5px 10px;
}
</style>