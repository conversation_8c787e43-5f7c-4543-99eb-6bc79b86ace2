<template>
  <div class="panel">
    <div class="config-card">
      <div class="config-card-head">
        <div class="card-header">
          <span class="header-text">考核类型</span>
        </div>
        <el-button type="success" size="small" icon="CirclePlus" @click="openExamineTypeDialog('add','')">新增
        </el-button>
      </div>
      <div class="config-card-body">
        <el-input v-model="filterText" placeholder="输入关键词检索" prefix-icon="el-icon-search" clearable
                  style="margin-bottom: 10px"/>
        <el-tree
          ref="examineTree"
          v-loading="examineLoading"
          :data="examineTreeData"
          :props="defaultProps"
          :default-expand-all="false"
          :expand-on-click-node="false"
          highlight-current
          :filter-node-method="filterNode"
          @node-click="nodeClick">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span>
                  <i class="el-icon-plus add-btn" v-if="node.level < 3" @click="openExamineTypeDialog('add', node)"></i>
                  <i class="el-icon-edit edit-btn" @click="openExamineTypeDialog('edit', node)"></i>
                  <i class="el-icon-delete del-btn" @click="deleteExamineType(node)"></i>
                  <i class="el-icon-view view-btn" v-if="node.level == 1" @click="viewConfigTree(node)"></i>
                </span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="config-right config-card">
      <div class="config-card-head">
        <div class="card-header">
          <span class="header-text-2" v-if="!isSelectedNodeEmpty">
                <span class="header-text">{{ selectedNode.typeLabel }}</span> -- {{ selectedNode.typeName }}
          </span>
        </div>
        <div>
          <el-button type="danger" size="small" icon="Delete" :disabled="tableSelection.length === 0"
                     @click="deleteExamineDetail('batch', '')">批量删除
          </el-button>
          <el-button type="success" size="small" icon="CirclePlus" :disabled="isSelectedNodeEmpty"
                     @click="openExamineDetailDialog('add','')">新增
          </el-button>
        </div>
      </div>
      <div v-if="examineDetailData.length > 0" class="scrollable-div">
        <el-collapse v-model="activeNames" class="config-detail" v-for="(item,index) in examineDetailData" :key="item.id" accordion>
          <el-collapse-item :name="index">
            <template #title>
              <div class="evaluation-item-title">
                <el-tooltip class="item" effect="dark" :content="item.evaluationItems" placement="top">
                  <span class="evaluation-item-text">{{ item.evaluationItems }}</span>
                </el-tooltip>
                <span class="evaluation-item-label">(评价项目)</span>
              </div>
            </template>
            <el-table v-adjust-table border :data="item.detailList" style="width: 100%" @selection-change="tableSelectionChange">
              <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
              <el-table-column prop="standard" label="评分标准" width="250" show-overflow-tooltip></el-table-column>
              <el-table-column prop="score" align="center" label="分值" width="80"></el-table-column>
              <el-table-column label="评分细化">
                <template slot-scope="scope">
                  <div class="detailed-evaluation" v-html="scope.row.detailedEvaluation"></div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" icon="el-icon-edit"
                             @click="openExamineDetailDialog('edit', scope.row)">修改
                  </el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete"
                             @click="deleteExamineDetail('single', scope.row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
      <el-empty v-else style="height:calc(100% - 51px); background-color: #fff; border-radius: 0 0 10px 10px;"></el-empty>
    </div>

    <el-dialog :title="examineTypeDialogTitle" :visible.sync="examineTypeDialog" width="30%">
      <el-form ref="examineTypeForm" :model="examineTypeForm" label-width="80px" :rules="typeRules">
        <el-form-item label="父级名称">
          <el-input v-model="examineTypeForm.parentName" disabled></el-input>
        </el-form-item>
        <el-form-item label="检查类型" prop="type" v-if="examineTypeForm.level == 1">
          <el-select v-model="examineTypeForm.type">
            <el-option v-for="dict in dict.type.examine_type" :key="dict.value" :value="dict.value"
                       :label="dict.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型标签" prop="typeLabel">
          <el-select v-model="examineTypeForm.typeLabel" placeholder="请选择类型标签" clearable>
            <el-option label="标题" value="标题"/>
            <el-option label="二级指标" value="二级指标"/>
            <el-option label="三级指标" value="三级指标"/>
          </el-select>
        </el-form-item>
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="examineTypeForm.typeName"></el-input>
        </el-form-item>
        <el-form-item label="权值" v-if="examineTypeForm.level > 1">
          <el-input-number v-model="examineTypeForm.weight" :precision="2" :step="0.1" :max="100"
                           style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="examineTypeForm.dataSort" :min="1" :max="50" style="width: 100%;"
                           placeholder="请输入排序"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="examineTypeDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveExamineType">保 存</el-button>
    </span>
    </el-dialog>

    <el-dialog :title="examineDetailDialogTitle" :visible.sync="examineDetailDialog" width="50%">
      <el-form ref="examineDetailForm" :model="examineDetailForm" label-width="80px" :rules="detailRules">
        <el-form-item label="评价项目" prop="evaluationItems">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}"
                    v-model="examineDetailForm.evaluationItems" placeholder="请输入评价项目"></el-input>
        </el-form-item>
        <el-form-item label="评分标准" prop="standard">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}"
                    v-model="examineDetailForm.standard"></el-input>
        </el-form-item>
        <el-form-item label="分值" prop="score">
          <el-input-number v-model="examineDetailForm.score" :precision="2" :step="0.1" :max="100"
                           style="width: 100%;" placeholder="请输入分值"></el-input-number>
        </el-form-item>
        <el-form-item label="评分细化" prop="detailedEvaluation">
          <editor v-model="examineDetailForm.detailedEvaluation" :min-height="192"/>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="examineDetailForm.dataSort" :min="1" :max="100" style="width: 100%;"
                           placeholder="请输入排序"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="examineDetailDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveExamineDetail">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="viewTreeDialogTitle" :visible.sync="viewTreeDialog" width="80%">
      <template #title>
        <span>{{ viewTreeDialogTitle }}</span>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExpAsXls(configTableData[0].typeName)" style="margin-left: 10px">导出</el-button>
      </template>
      <el-table border :data="flatData" :span-method="spanMethod" :header-row-style="{ height: '100px' }" id='outTable'>
        <el-table-column label="标题" align="center" prop="title" width="50"></el-table-column>
        <el-table-column label="二级指标" align="center" prop="secondary" width="80"></el-table-column>
        <el-table-column label="二级权值" align="center" prop="secondaryWeight" width="80"></el-table-column>
        <el-table-column label="三级指标" align="center" prop="tertiary" width="80"
                         v-if="flatData.some(item => item.tertiary)"></el-table-column>
        <el-table-column label="三级权值" align="center" prop="tertiaryWeight" width="80"
                         v-if="flatData.some(item => item.tertiaryWeight)"></el-table-column>
        <el-table-column label="评价项目" align="center" prop="evaluationItems" width="100"></el-table-column>
        <el-table-column label="评分标准" prop="standard" width="300"></el-table-column>
        <el-table-column label="分值" align="center" prop="score" width="60"></el-table-column>
        <el-table-column label="评分细化">
          <template slot-scope="scope">
            <div class="detailed-evaluation" v-html="scope.row.detailedEvaluation"></div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  addExamineDetail,
  addExamineType,
  deleteDetail,
  deleteType,
  editExamineDetail,
  editExamineType,
  queryExamineAssociationTree,
  queryExamineDetailList,
  queryExamineTypeList
} from '@/api/examine/config/examineType'
import FileSaver from 'file-saver'
import * as XLSX from 'xlsx'  // 用于 table_to_book
import XLSX_STYLE from 'xlsx-style'  // 用于样式和导出

export default {
  name: 'examine-config',
  dicts: [
    'examine_type',  // 考核类型
  ],
  data() {
    return {
      examineLoading: false,
      examineTreeData: [],
      examineDetailData: [],
      filterText: '',
      activeNames: [0],
      selectedNode: {},
      defaultProps: {
        children: 'children',
        label: 'typeName'
      },
      examineTypeDialog: false,
      examineTypeDialogTitle: '',
      examineTypeForm: {
        id: '',
        level: 1,
        typeLabel: '',
        typeName: '',
        parentId: '',
        parentName: '',
        type: '',
        rootId: '',
        weight: 0,
        dataSort: 0
      },
      examineDetailDialog: false,
      examineDetailDialogTitle: '',
      viewTreeDialog: false,
      viewTreeDialogTitle: '',
      tableList: [],
      configTableData: [],
      examineDetailForm: {
        id: '',
        examineTypeId: '',
        evaluationItems: '',
        standard: '',
        score: 0,
        detailedEvaluation: '',
        dataSort: 0
      },
      tableSelection: [], // 表格选中数据
      typeRules: {
        type: [
          {required: true, message: '请选择检查类型', trigger: 'blur'},
        ],
        typeLabel: [
          {required: true, message: '请输入类型标签', trigger: 'blur'},
        ],
        typeName: [
          {required: true, message: '请输入类型名称', trigger: 'blur'},
        ]
      },
      detailRules: {
        evaluationItems: [
          {required: true, message: '请输入评价项目', trigger: 'blur'},
        ],
        standard: [
          {required: true, message: '请输入评分标准', trigger: 'blur'},
        ],
        score: [
          {required: true, message: '请输入分值', trigger: 'blur'},
        ],
        detailedEvaluation: [
          {required: true, message: '请输入评分细化', trigger: 'blur'},
        ]
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.examineTree.filter(val)
    }
  },
  computed: {
    isSelectedNodeEmpty() {
      return !this.selectedNode || Object.keys(this.selectedNode).length === 0;
    },
    flatData() {
      const result = [];

      const traverse = (item, titles = [], weights = []) => {
        // 处理当前节点
        if (item.level === 1) {
          titles[0] = item.typeName; // 一级指标
        } else if (item.level === 2) {
          titles[1] = item.typeName; // 二级指标
          weights[1] = item.weight;   // 二级指标权重

          // 如果二级指标没有子节点，清除三级指标数据
          if (!item.children || item.children.length === 0) {
            titles[2] = ""; // 清除三级指标名称
            weights[2] = ""; // 清除三级指标权重
          }
        } else if (item.level === 3) {
          titles[2] = item.typeName; // 三级指标
          weights[2] = item.weight;   // 三级指标权重
        }

        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            traverse(child, titles, weights); // 传递当前值
          });
        } else {
          // 叶子节点
          result.push({
            id: item.id,
            level: item.level,
            typeLabel: item.typeLabel,
            typeName: item.typeName,
            parentId: item.parentId,
            parentName: item.parentName,
            dataSort: item.dataSort,
            title: titles[0] || "", // 一级指标名称
            secondary: titles[1] || "", // 二级指标名称
            secondaryWeight: weights[1] || "", // 二级指标权重
            tertiary: titles[2] || "", // 三级指标名称
            tertiaryWeight: weights[2] || "", // 三级指标权重
            evaluationItems: item.evaluationItems,
            standard: item.standard,
            score: item.score,
            detailedEvaluation: item.detailedEvaluation
          });
        }
      };

      this.configTableData.forEach(item => {
        if (item.level === 1) {
          traverse(item); // 初始调用
        }
      });

      console.log(result)
      return result; // 返回所有数据
    },
    flatTypeData() {
      const result = [];

      const traverse = (item, titles = [], weights = []) => {
        // 处理当前节点
        if (item.level === 1) {
          titles[0] = item.typeName; // 一级指标
        } else if (item.level === 2) {
          titles[1] = item.typeName; // 二级指标
          weights[1] = item.weight;   // 二级指标权重

          // 如果二级指标没有子节点，清除三级指标数据
          if (!item.children || item.children.length === 0) {
            titles[2] = ""; // 清除三级指标名称
            weights[2] = ""; // 清除三级指标权重
          }
        } else if (item.level === 3) {
          titles[2] = item.typeName; // 三级指标
          weights[2] = item.weight;   // 三级指标权重
        }

        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            traverse(child, titles, weights); // 传递当前值
          });
        } else {
          // 叶子节点
          result.push({
            id: item.id,
            level: item.level,
            typeLabel: item.typeLabel,
            typeName: item.typeName,
            parentId: item.parentId,
            parentName: item.parentName,
            dataSort: item.dataSort,
            title: titles[0] || "", // 一级指标名称
            secondary: titles[1] || "", // 二级指标名称
            secondaryWeight: weights[1] || "", // 二级指标权重
            tertiary: titles[2] || "", // 三级指标名称
            tertiaryWeight: weights[2] || "" // 三级指标权重
          });
        }
      };

      this.typeTableData.forEach(item => {
        if (item.level === 1) {
          traverse(item); // 初始调用
        }
      });

      console.log(result)
      return result; // 返回所有数据
    }
  },
  created() {
    this.getExamineTree()
  },
  methods: {
    async getExamineTree() {
      this.examineLoading = true
      try {
        const res = await queryExamineTypeList()
        if (res.code == 200) {
          this.examineTreeData = res.data
        }
      } catch (error) {
      } finally {
        this.examineLoading = false
      }
    },
    async getExamineDetailList(id) {
      let vo = {
        examineTypeId: id
      }
      const res = await queryExamineDetailList(vo)
      if (res.code == 200) {
        this.examineDetailData = res.data
      }
    },
    async getExamineAssociationTree(id) {
      let vo = {
        id: id
      }
      const res = await queryExamineAssociationTree(vo)
      if (res.code == 200) {
        this.configTableData = res.data
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.typeName.indexOf(value) !== -1
    },
    nodeClick(e) {
      this.selectedNode = {}
      this.selectedNode = e
      this.getExamineDetailList(e.id)
    },
    treeFilterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    treeNodeClick(e) {
      this.title = e.culvertCode
      this.treeIds = [e.assetId]
    },
    openExamineTypeDialog(type, node) {
      let levelOneId = null;
      if (node.level === 1) {
        levelOneId = node.data.id;
      } else {
        let parent = node.parent;
        while (parent) {
          if (parent.level === 1) {
            levelOneId = parent.data.id;
            break;
          }
          parent = parent.parent;
        }
      }

      this.resetTypeForm()
      if (type == 'add') {
        this.examineTypeDialogTitle = '新增考核类型'
        if (node) {
          this.examineTypeForm.level = node.data.level + 1
          this.examineTypeForm.parentId = node.data.id
          this.examineTypeForm.parentName = node.data.typeName
          this.examineTypeForm.rootId = levelOneId;
        } else {
          this.examineTypeForm.level = 1
          this.examineTypeForm.parentId = ''
          this.examineTypeForm.parentName = 'ROOT'
          this.examineTypeForm.rootId = '';
        }
      } else {
        this.examineTypeDialogTitle = '编辑考核类型'
        this.examineTypeForm = {
          id: node.data.id,
          level: node.data.level,
          typeLabel: node.data.typeLabel,
          typeName: node.data.typeName,
          parentId: node.data.parentId,
          parentName: node.data.parentName,
          weight: node.data.weight,
          dataSort: node.data.dataSort,
          type: node.data.type,
          rootId: levelOneId
        }
      }
      this.examineTypeDialog = true
    },
    async saveExamineType() {
      // 验证表单
      this.$refs.examineTypeForm.validate(async valid => {
        if (!valid) return;
        try {
          let response;
          // 判断是更新还是新增
          if (this.examineTypeForm.id) {
            response = await editExamineType(this.examineTypeForm);
            console.log('数据更新结果：', response);
          } else {
            response = await addExamineType(this.examineTypeForm);
            console.log('数据新增结果：', response);
          }
          // 检查返回结果
          if (response.code === 200) {
            this.$message.success('操作成功！');
            this.examineTypeDialog = false;
            this.getExamineTree(); // 刷新树结构
          }
        } catch (error) {
          console.error('操作失败:', error);
          this.$message.error('操作失败，请重试！');
        }
      });
    },
    openExamineDetailDialog(type, row) {
      this.resetDetailForm()
      if (type == 'add') {
        this.examineDetailDialogTitle = '新增考核明细--' + this.selectedNode.typeName
        this.examineDetailForm.examineTypeId = this.selectedNode.id
      } else {
        this.examineDetailDialogTitle = '编辑考核明细'
        this.examineDetailForm = {
          id: row.id,
          examineTypeId: row.examineTypeId,
          evaluationItems: row.evaluationItems,
          standard: row.standard,
          score: row.score,
          detailedEvaluation: row.detailedEvaluation,
          dataSort: row.dataSort
        }
      }
      this.examineDetailDialog = true
    },
    async saveExamineDetail() {
      // 验证表单
      this.$refs.examineDetailForm.validate(async valid => {
        if (!valid) return;
        try {
          let response;
          // 判断是更新还是新增
          if (this.examineDetailForm.id) {
            response = await editExamineDetail(this.examineDetailForm);
            console.log('数据更新结果：', response);
          } else {
            response = await addExamineDetail(this.examineDetailForm);
            console.log('数据新增结果：', response);
          }
          // 检查返回结果
          if (response.code === 200) {
            this.$message.success('操作成功！');
            this.examineDetailDialog = false;
            await this.getExamineDetailList(this.selectedNode.id); // 刷新
          }
        } catch (error) {
          console.error('操作失败:', error);
          this.$message.error('操作失败，请重试！');
        }
      });
    },
    deleteExamineType(node) {
      this.$confirm('删除树形节点会关联删除配置明细，您确定删除该数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const deleteId = node.data.id;
        const res = await deleteType(deleteId);
        if (res && res.code === 200) {
          this.$message.success('操作成功！');
          await this.getExamineTree();
        }
      })
    },
    viewConfigTree(node) {
      this.getExamineAssociationTree(node.data.id)
      this.viewTreeDialogTitle = node.data.typeName
      this.viewTreeDialog = true
    },
    async handleExpAsXls(val) {
      // 1. 获取表格数据
      const wb = XLSX.utils.table_to_book(document.querySelector('#outTable'));

      // 2. 获取工作表对象
      const ws = wb.Sheets[wb.SheetNames[0]];

      // 3. 设置样式
      this.setExcelStyles(ws);

      // 4. 导出文件
      const wbout = XLSX_STYLE.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'buffer'
      });

      try {
        FileSaver.saveAs(
          new Blob([wbout], { type: 'application/octet-stream' }),
          val + '.xlsx'
        );
      } catch (e) {
        if (typeof console !== 'undefined') console.log(e, wbout);
      }
      return wbout;
    },
    // 设置Excel样式的方法
    setExcelStyles(ws) {
      // 确定表格范围
      const range = XLSX.utils.decode_range(ws['!ref']);

      // 设置行高 - 修正行数计算
      ws['!rows'] = [
        { hpt: 80 },  // 第一行（表头）高度80
      ];

      // 设置自定义列宽（示例值，请根据实际需求调整）
      ws['!cols'] = [
        { wch: 10 },
        { wch: 15 },
        { wch: 7 },
        { wch: 15 },
        { wch: 10 },
        { wch: 20 },
        { wch: 30 },
        { wch: 10 },
        { wch: 50 },
      ];

      // 为所有单元格设置边框
      for (let R = range.s.r; R <= range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          const cell = ws[cellAddress];

          // 如果单元格不存在，创建一个空对象
          if (!cell) {
            ws[cellAddress] = { t: 's', v: '' }; // 创建空字符串单元格
          }

          // 基础样式设置
          const baseStyle = {
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            },
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center',   // 垂直居中
              wrapText: true        // 自动换行（可选）
            }
          };

          // 如果是第一行（表头），添加特殊样式
          if (R === 0) {
            ws[cellAddress].s = {
              ...baseStyle,
              font: {
                bold: true,        // 加粗
                sz: 14,           // 字体大小14
                color: { rgb: '000000' } // 黑色字体
              },
              fill: {
                fgColor: { rgb: 'D3D3D3' } // 灰色背景（可选）
              }
            };
          } else {
            ws[cellAddress].s = baseStyle;
          }
        }
      }
    },
    tableSelectionChange(val) {
      this.tableSelection = val
    },
    deleteExamineDetail(type, item) {
      this.$confirm('确定删除该数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const deleteId = type === 'single' ? item.id : this.tableSelection.map(row => row.id).join(',');
          try {
            const res = await deleteDetail(deleteId);
            if (res && res.code === 200) {
              this.$message.success('操作成功！');
              await this.getExamineDetailList(this.selectedNode.id);
            }
          } catch (err) {
            this.$message.error(err);
          }
        });
    },

    resetTypeForm() {
      this.examineTypeForm = {
        id: '',
        level: 1,
        typeLabel: '',
        typeName: '',
        parentId: '',
        weight: '',
        dataSort: 0,
        type: ''
      }
    },
    resetDetailForm() {
      this.examineDetailForm = {
        id: '',
        examineTypeId: '',
        evaluationItems: '',
        standard: '',
        score: '',
        detailedEvaluation: '',
        dataSort: 0
      }
    },
    mergeCells({row, column, rowIndex}) {
      const mergeColumns = ['title', 'secondary', 'secondaryWeight', 'tertiary', 'tertiaryWeight'];

      const getRowspan = (prop) => {
        const isSameAsPrevious = rowIndex > 0 && row[prop] === this.flatTypeData[rowIndex - 1][prop];
        const isSameSecondary = isSameAsPrevious && (prop === 'secondaryWeight' ? row.secondary === this.flatTypeData[rowIndex - 1].secondary : true);

        if (isSameAsPrevious) {
          if (isSameSecondary) {
            return {rowspan: 0, colspan: 1}; // 当前单元格隐藏
          }
          let count = 1;
          for (let i = rowIndex + 1; i < this.flatTypeData.length; i++) {
            if (this.flatTypeData[i][prop] === row[prop] && (prop !== 'secondaryWeight' || this.flatTypeData[i].secondary === row.secondary)) {
              count++;
            } else {
              break;
            }
          }
          return {rowspan: count, colspan: 1};
        } else {
          let count = 1;
          for (let i = rowIndex + 1; i < this.flatTypeData.length; i++) {
            if (this.flatTypeData[i][prop] === row[prop] && (prop !== 'secondaryWeight' || this.flatTypeData[i].secondary === row.secondary)) {
              count++;
            } else {
              break;
            }
          }
          return {rowspan: count, colspan: 1};
        }
      };

      // 仅对需要合并的字段进行处理
      const columnProp = column.property;
      if (mergeColumns.includes(columnProp)) {
        return getRowspan(columnProp);
      }
      return {rowspan: 1, colspan: 1}; // 不合并的字段返回默认值
    },

    spanMethod({row, column, rowIndex}) {
      const mergeableFields = ['title', 'secondary', 'secondaryWeight', 'tertiary', 'tertiaryWeight', 'evaluationItems'];

      const getRowspan = (prop) => {
        const isSameAsPrevious = rowIndex > 0 && row[prop] === this.flatData[rowIndex - 1][prop];
        const isSameSecondary = isSameAsPrevious && (prop === 'secondaryWeight' ? row.secondary === this.flatData[rowIndex - 1].secondary : true);

        if (isSameAsPrevious) {
          if (isSameSecondary) {
            return {rowspan: 0, colspan: 1}; // 当前单元格隐藏
          }
          let count = 1;
          for (let i = rowIndex + 1; i < this.flatData.length; i++) {
            if (this.flatData[i][prop] === row[prop] && (prop !== 'secondaryWeight' || this.flatData[i].secondary === row.secondary)) {
              count++;
            } else {
              break;
            }
          }
          return {rowspan: count, colspan: 1};
        } else {
          let count = 1;
          for (let i = rowIndex + 1; i < this.flatData.length; i++) {
            if (this.flatData[i][prop] === row[prop] && (prop !== 'secondaryWeight' || this.flatData[i].secondary === row.secondary)) {
              count++;
            } else {
              break;
            }
          }
          return {rowspan: count, colspan: 1};
        }
      };

      // 仅对需要合并的字段进行处理
      const columnProp = column.property;
      if (mergeableFields.includes(columnProp)) {
        return getRowspan(columnProp);
      }
      return {rowspan: 1, colspan: 1}; // 不合并的字段返回默认值
    }

  }
}
</script>

<style lang="scss" scoped>
.panel {
  width: 100%;
  height: 100%;
  background-color: #c0c0c0;
  padding: 10px;
  display: flex;

  .config-card {
    min-width: 400px;
    height: 100%;
    background-color: #fff;
    position: relative;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);

    .config-card-head {
      width: 100%;
      height: 51px;
      background: #f2f3f5;
      color: #212529 !important;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      padding: 10px;

      .card-header {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .header-text {
          font-size: 16px;
          font-weight: 600;
        }

        .header-text-2 {
          font-size: 15px;
        }
      }
    }

    .config-card-body {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 51px);
      padding: 15px 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
      }

      .add-btn {
        color: rgba(2, 194, 90, 1);
        margin: 5px;
        cursor: pointer;
      }

      .del-btn {
        color: rgba(255, 72, 95, 1);
        margin: 5px;
        cursor: pointer;
      }

      .edit-btn {
        color: rgba(245, 166, 35, 1);
        margin: 5px;
        cursor: pointer;
      }

      .view-btn {
        color: rgb(38, 155, 164);
        margin: 5px;
        cursor: pointer;
      }

      .drawing-card-body-selectBox {
        margin-top: 10px;
        display: flex;
        align-items: center;
        border: 1px solid #dcdfe6;
        height: 40px;
        width: 100%;
        border-radius: 4px;
        padding-left: 10px;

        .el-radio {
          margin-right: 8px;
        }
      }
    }
  }

  .config-right {
    width: 100%;
    height: 100%;
    margin-left: 10px;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);

    .scrollable-div {
      height: calc(100% - 51px); /* 设置固定高度 */
      overflow-y: auto; /* 垂直方向显示滚动条 */
      border: 1px solid #ccc; /* 可选：边框样式 */
    }

    .config-detail {
      padding: 10px;

      .evaluation-item-title {
        display: flex;
        align-items: center;
        width: 100%; /* 根据实际容器宽度调整 */
        max-width: 400px; /* 设置最大宽度，超出部分显示省略号 */
      }

      .evaluation-item-text {
        white-space: nowrap; /* 禁止换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        flex-shrink: 1; /* 允许缩小 */
      }

      .evaluation-item-label {
        font-weight: normal; /* 设置字体样式 */
        color: #999; /* 设置字体颜色 */
        margin-left: 5px; /* 左侧间距 */
        font-weight: 600;
        font-size: 13px; /* 字体大小，可以根据需要调整 */
      }
    }

    ::v-deep .el-collapse-item__header {
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden; /* 超出隐藏 */
      text-overflow: ellipsis; /* 添加省略号 */
    }
  }
}

.detailed-evaluation {
  line-height: 15px;
}

::v-deep .el-tree-node__content {
  &:hover {
    background-color: #b7daff;
  }
}

::v-deep
.el-tree--highlight-current
.el-tree-node.is-current
> .el-tree-node__content {
  background-color: #b7daff;
}

::v-deep .el-radio__label {
  padding-left: 5px !important;
}
</style>
