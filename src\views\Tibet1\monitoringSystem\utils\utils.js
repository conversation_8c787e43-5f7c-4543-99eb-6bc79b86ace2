// 判断是否是大屏
export function isBigScreen() {
  let availW = window.innerWidth || window.screen.width || window.screen.availWidth || 1920;
  let availH = window.innerHeight || window.screen.height || window.screen.availHeight || 1080;
  let isBigScreen = false;
  if (availW > 2880 && availH > 1440) {
    isBigScreen = true;
  }
  return isBigScreen;
}

// 监听是否全屏
export function nowSize(val) {
  let availW = window.innerWidth || window.screen.width || window.screen.availWidth || 1920;
  let availH = window.innerHeight || window.screen.height || window.screen.availHeight || 1080;
  let nowClientWidth = availW;
  let initWidth = 1920;
  if (availW > 2880 && availH > 1440) {
    nowClientWidth = availW;
    initWidth = 3840;
  }
  return val * (nowClientWidth / initWidth);
}

// 获取当前地址栏参数
export function getUrlParam() {
  let url = window.location.href;
  let params = url.split("?")[1];
  // 解密参数
  params = decodeURIComponent(params);
  let obj = {};
  if (params) {
    let arr = params.split("&");
    if (arr.length > 0) {
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i].split("=");
        obj[item[0]] = item[1];
      } 
    }
  }
  return obj;
}