<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
              <el-form-item>
                <selectTree
                  :key="'domainId'"
                  style="width: 240px"
                  v-model="queryParams.domainId"
                  :deptType="100" :deptTypeList="[1, 3, 4]"
                  placeholder="管养单位"
                  clearable
                  filterable
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'constructionUnit'"
                  style="width: 240px"
                  v-model="queryParams.conDomainId" :data-rule="false"
                  :dept-type="100"
                  :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                  :expand-all="false"
                  placeholder="施工单位"
                  clearable
                  filterable
                />
              </el-form-item>
              <el-form-item>
                <RoadSection style="width: 240px" v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"/>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="项目名称" v-model="queryParams.projName"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                     label-width="68px">
              <el-form-item>
                <el-input style="width: 240px" placeholder="项目编码" v-model="queryParams.projCode"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select type="task_type" v-model="queryParams.type" placeholder="任务单类型"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="任务单名称" v-model="queryParams.name"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="任务单编码" v-model="queryParams.code"></el-input>
              </el-form-item>
              <el-form-item v-if='status == 0'>
                <el-select v-model="queryParams.visaStatus" placeholder="提交状态" clearable>
                  <el-option label="待提交的签证单" :value="0">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="status == -1">
                <el-input style="width: 240px" placeholder="签证单编码" v-model="queryParams.finishedCode"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select type="project_construction_status" v-if="status == '-1'" v-model="queryParams.status" placeholder="状态"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item label="" prop="operator" v-if="status == -1">
                <el-cascader
                  v-model="queryParams.operator"
                  :options="deptUserOptions"
                  :props="{
                    multiple: false, //是否多选
                    value: 'id',
                    emitPath: false,
                  }"
                  :show-all-levels="false"
                  ref="deptUser"
                  placeholder="通知单拟定人" clearable style="width: 240px"
                  filterable clearable></el-cascader>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['theft:construction:pendingexport', 'theft:finished:export', 'theft:construction:completedexport']"
              @click="exportList"
            >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-view"
              size="mini"
              type="warning"
              @click="handleOpenOperate"
            >审核意见
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
            size="mini"
            style="width: 100%"
            v-loading="loading"
            border
            :data="tableData"
            row-key="id"
            ref="dataTable"
            stripe
            highlight-current-row
            @row-click="handleClickRow"
            :height="
                showSearch ? 'calc(100vh - 430px)' : 'calc(100vh - 370px)'
              "
          >
            <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible" show-overflow-tooltip
                               align="center"
                               :prop="column.field"
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="250"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <slot name="operate" :scope="scope"></slot>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView (scope.row)"
                >签证单详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" title="签证单详情" destroy-on-close :visible.sync="openView" v-if="openView" size="70%">
      <reg-detail @query="handleQuery" :row-data="row" :status="status"></reg-detail>
    </el-drawer>
    <el-dialog title="操作意见" :visible.sync="openOperateInfo" v-if="openOperateInfo" width="80%" destroy-on-close>
      <operateInfo type="theft" :business-key="clickRow.id" :get-node-info="getNodeInfo"></operateInfo>
    </el-dialog>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {completedList, getNodeInfo, pendingListConstruction} from "@/api/theft/taskList";
import {queryList, queryProcessList} from "@/api/theft/construction";
import RegDetail from "./regDetail.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {getTreeStruct} from "@/api/tmpl";

export default {
  components: {
    operateInfo,
    RegDetail, selectTree, RoadSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['project_construction_status', 'task_type', 'project_type', 'affiliation_project_type'],
  props: {
    status: {
      type: String,
      default: ''
    }
  },
  provide() {
    return {
      getTaskData: this.getTaskData
    };
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      openOperateInfo: false,
      columns: [
        {key: 0, width: 100, field: 'timeOutMsg', label: `超期提醒`, visible: true},
        {key: 1, width: 100, field: 'isEnd', label: `是否完结`, visible: true, slots: true,
          render: (row) => {
            if (row.isEnd == 1) {
              return (
                  <el-tag type="success">已完结</el-tag>
              )
            } else {
              return (
                  <el-tag type="danger">未完结</el-tag>
              )
            }
          }},
        {key: 2, width: 100, field: 'status', label: `施工状态`, visible: true, dict: 'project_construction_status'},
        {key: 3, width: 100, field: 'visaCount', label: `签证单总数`, visible: true},
        {key: 4, width: 100, field: 'visaFund', label: `签证金额`, visible: true},
        {key: 5, width: 200, field: 'projName', label: `项目名称`, visible: true},
        {key: 5, width: 200, field: 'projCode', label: `项目编码`, visible: this.status == -1},
        {key: 6, width: 100, field: 'type', label: `任务单类型`, visible: true, dict: 'task_type'},
        {key: 7, width: 200, field: 'name', label: `任务单名称`, visible: true},
        {key: 8, width: 200, field: 'code', label: `任务单编号`, visible: true},
        {key: 9, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 10, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 11, width: 200, field: 'conDomainName', label: `施工单位`, visible: true},
        {key: 12, width: 200, field: 'conConName', label: `合同`, visible: true},
        {key: 13, width: 200, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 14, width: 200, field: 'supConName', label: `监理合同`, visible: true},
        {key: 15, width: 200, field: 'designDomainName', label: `设计单位`, visible: true},
        {key: 16, width: 100, field: 'defLiaPer', label: `缺陷责任期(月)`, visible: true},
        {key: 17, width: 200, field: 'content', label: `工作内容`, visible: true},
        {key: 18, width: 200, field: 'exeRequire', label: `实施要求`, visible: true},
        {key: 19, width: 100, field: 'beginDate', label: `计划开始时间`, visible: true},
        {key: 20, width: 100, field: 'endDate', label: `计划完成时间`, visible: true},
      ],
      openView: false,
      row: {},
      clickRow: {},
      tableData: [],
      deptUserOptions: []
    }
  },
  mounted() {
    this.getDeptTreeDef()
    this.handleQuery()
  },
  methods:{
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    getNodeInfo,
    handleQuery() {
      this.loading = true
      if(this.status == '0') {
        this.queryParams.status = 5
        completedList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else if (this.status == '-1') {
        queryList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else {
        this.queryParams.visaStatus = this.status
        queryProcessList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleView(row) {
      this.row = row
      this.openView = true
    },
    getTaskData() {
      return this.row
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    handleOpenOperate() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.openOperateInfo = true
    },
    // 导出清单按钮
    exportList() {
      let url = ''
      if (this.status == '0') { // 完工登记
        this.queryParams.status = 5
        url = 'manager/theft/construction/completed/export'
      } else if (this.status == '-1') { // 施工进度
        url = 'manager/theft/construction/finished/export'
      } else {  // 施工单位审核、监理审核、验收登记
        this.queryParams.visaStatus = this.status
        url = 'manager/theft/construction/finished/process/export'
      }
      this.download(
        url,
        {...this.queryParams},
        `finished_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
