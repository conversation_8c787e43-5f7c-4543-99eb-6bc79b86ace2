<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="budgetCode">
                <el-input v-model="queryParams.budgetCode" placeholder="请输入版本编码" :maxlength="100" clearable
                    :style="{width: '100%'}"></el-input>
              </el-form-item>
              <el-form-item label="" prop="code">
                <el-select v-model="queryParams.budgetVer" placeholder="请选择类型版本" clearable :style="{width: '100%'}">
                  <el-option v-for="(item, index) in versionList" :key="index" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                v-has-menu-permi="['budget:typeconfig:add']"
                icon="el-icon-plus"
                size="mini"
                type="primary"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
              highlight-current-row
               @current-change="handleCurrentChange"
              :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
              row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              border size="mini" style="width: 100%">
            <el-table-column fixed label="序号" align="center" type="index" width="100"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="300"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['budget:typeconfig:remove']"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['budget:typeconfig:edit']"
                    icon="el-icon-edit"
                    @click="handleEdit(scope.row)"
                >编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog :title="dialogTitle" :visible.sync="open" destroy-on-close append-to-body width="500px">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-form-item label="类型版本" prop="budgetVer">
          <el-select v-model="formData.budgetVer" placeholder="请选择类型版本" clearable :style="{width: '100%'}" :disabled="currentOption==='edit'">
            <el-option v-for="(item, index) in versionList" :key="index" :label="item.label"
                      :value="item.value" :disabled="item.disabled">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型编码" prop="budgetCode">
          <el-input v-model="formData.budgetCode" placeholder="请输入类型编码" :maxlength="100" clearable
                    :style="{width: '100%'}"></el-input>
        </el-form-item>
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="formData.typeName" placeholder="请输入类型名称" :maxlength="100" clearable
          :style="{width: '100%'}"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sortIndex">
          <el-input v-model="formData.sortIndex" placeholder="请输入排序" type="number" clearable
                    :style="{width: '100%'}"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetTypeconfigList , AddTypeconfigList, EditTypeconfigList, DeleteTypeconfig } from "@/api/budgetManage/typeConfig";
import { listAllBudgetVersions} from "@/api/budgetManage/version";
import { formatDate } from '@/utils/index'
export default {
  name: "TypeConfig",
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      open: false,
      // 列信息
      columns: [
        {key: 0, field: 'typeName', label: `类型名称`, visible: true},
        {key: 1, field: 'budgetCode', label: `类型编码`, visible: true},
        {key: 2, field: 'grade', label: `类型级别`, visible: true},
        {key: 3, field: 'sortIndex', label: `排序`, visible: true},
        {key: 4, field: 'createTime', label: `编制时间`, visible: true, slots: true, render: (row, index) => {
            const time = row.createTime
            return (<span>{ formatDate(time) }</span>)
        }},

      ],
      formData: {
        budgetCode: "",
        typeName: "",
      },
      rules: {
        budgetVer: [{
          required: true,
          message: '请选择类型版本',
          trigger: 'change'
        }],
        budgetCode: [{
          required: true,
          message: '请输入类型编码',
          trigger: 'blur'
        }],
        typeName: [{
          required: true,
          message: '请输入类型名称',
          trigger: 'blur'
        }],
      },
      options: [{
        "label": "关闭",
        "value": 0
      }, {
        "label": "开启",
        "value": 1
      }],
      versionList: [],
      currentRow: null,
      currentOption:"add",
      dialogTitle:"新增"
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.handleQuery()
    this.getAllVersions()
  },
  methods: {
    // 查询所有版本
    getAllVersions() {
      listAllBudgetVersions().then(res => {
        this.versionList = res.data.map(el=>{
          return {value:el.id,label:el.name}
        })
      })
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
    },
    handleQuery() {
      this.loading = true
      GetTypeconfigList(this.queryParams).then(res => {
        // 组装树形数据
        this.dataList = this.handleTree(res.rows, "id");
        // this.dataList = this.formatToTree(res.rows)
        console.log("this.dataList",this.dataList)
        this.total = res.total
        this.loading = false
      })
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    formatToTree(ary,pid) {
      return ary
        .filter((item) =>
          // 如果没有父id（第一次递归的时候）将所有父级查询出来
          // 这里认为 item.parentId === 1 就是最顶层 需要根据业务调整
          pid === undefined ? item.parentId === '0' : item.parentId === pid
        )
        .map((item) => {
          // 通过父节点ID查询所有子节点
          item.children = this.formatToTree(ary, item.id);
          if(item.children.length>0){
            item.hasChildren = true
          }
          return item;
        });
    },
    handleAdd() {
      this.dialogTitle = "新增"
      this.$nextTick(() => {
        this.$refs['elForm'].clearValidate()
      })
      this.currentOption = "add"
      this.formData = {
        parentId:this.currentRow?this.currentRow.id:'0',
        budgetCode: "",
        typeName: ""
      }
      this.open = true
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        if(this.currentOption === "add"){
          AddTypeconfigList(this.formData).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.handleQuery()
            } else {
              this.loading = false
              this.$modal.msgError(res.msg)
            }
            this.close()
          })
        }else{
          EditTypeconfigList(this.formData).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess("更新成功")
              this.open = false
              this.handleQuery()
            } else {
              this.loading = false
              this.$modal.msgError(res.msg)
            }
          })
        }
      })
    },
    handleDelete(row) {
      if (row.children && row.children.length > 0) {
        this.$modal.msgError("请先删除子元素")
        return
      }
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        DeleteTypeconfig(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    handleEdit(row) {
      this.dialogTitle = "编辑"
      this.currentOption = "edit"
      this.formData = {
        id: row.id,
        budgetCode: row.budgetCode,
        typeName:  row.typeName,
        budgetVer:  row.budgetVer,
        sortIndex: row.sortIndex
      }
      this.open = true
    },
    close() {
      this.open = false
    },
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
