<template>
    
    <el-tree
        :data="filteredTreeData"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :default-expanded-keys="[1]"
        ref="tree"
        node-key="id"
        highlight-current
        @node-click="handleNodeClick"
        >
    </el-tree>
</template>

<script>

import {getTreeStruct} from '@/api/tmpl.js'
import {
    getMaintenanceSectionListAll,
} from "@/api/baseData/common/routeLine";
export default { // 路段选择器
    name: 'MaintenanceSectionTree',
    components: {},
    props: {
       
        queryParams:{
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            bridgeLoding: false,
            filteredTreeData:[],
            relaOptions: [],
        }
    },

    computed: {
       
        formData: {
            get: function (val) {
                return this.queryParams
            },
           
        },
        selectValue: {
            get: function (val) {
              
                this.$emit('change', val)
            },
            set: function(val) {
                
                this.$emit('input', val)
            }
        },
        
    },
    created() {
        this.getDeptTree();

    },
    mounted() { 
       
    },
    methods: {
            // 查询部门下拉树结构
            getDeptTree() {
                getTreeStruct({types: 101}).then(response => {
                    let datalist=JSON.parse(JSON.stringify(response.data))
                    getMaintenanceSectionListAll().then((res)=>{
                    //对比datalist的id和res.data中的departmentId,如果相同,就放在datalist下对应的children中
                    datalist.forEach((item)=>{
                        item.children=[]
                        res.data.forEach((resItem)=>{
                        if(item.id==resItem.departmentId){
                            console.log(item)
                            item.children.push({id:resItem.maintenanceSectionId,label:resItem.maintenanceSectionName})
                        }
                        })
                    })
                    })
                    this.relaOptions = [...datalist]
                    this.filteredTreeData = [...this.relaOptions]
                    this.queryParams.managementMaintenanceIds = [this.relaOptions[0].id]
                    this.queryParams.managementMaintenanceId = [this.relaOptions[0].id]
                });
            },
           // 筛选节点
            filterNode(node, keyword) {
                if (node.label.indexOf(keyword) != -1) {
                    return true;
                }
                if (node.children) {
                    return node.children.some(childNode => this.filterNode(childNode, keyword));
                }
                return false;
            },
            handleNodeClick(e) {
                this.bridgeLoding=true
                if (e.label.includes('管理处')) {
                    this.formData.managementMaintenanceId = [e.id]
                    this.formData.managementMaintenanceIds= [e.id]
                    this.formData.maintenanceSectionId = undefined
                    this.formData.maintenanceSectionIds = undefined
                }else{
                    this.formData.maintenanceSectionId = e.id
                    this.formData.maintenanceSectionIds = [e.id]
                    this.formData.managementMaintenanceId = undefined
                    this.formData.managementMaintenanceIds= undefined
                }
                // this.handleBridge()
                this.$emit('customEvent');
                
            },
        }
}
</script>



<style lang="scss" scoped>
.inputStyle {
    color: black;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    padding-left: 15px;
    min-height: 35px;
}
</style>