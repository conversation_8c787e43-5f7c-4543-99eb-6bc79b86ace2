export const report_set_rule = {
  structureName: [
    {required: true, message: '配置名称不可为空', trigger: 'blur'}
  ],
  startChapter: [
    {required: true, message: '开始章节不可为空', trigger: 'blur'}
  ],
}

export const edit_set_rule = {
  content: [
    {required: true, message: '节点内容不可为空', trigger: 'blur'}
  ],
  algorithmName: [
    {required: true, message: '绘图种类不可为空', trigger: 'blur'}
  ],
  title: [
    {required: true, message: '绘图标题名称不可为空', trigger: 'blur'}
  ],
}

export const addSensor_set_rule = {
  eigenvalueType: [
    {required: true, message: '特征值种类不可为空', trigger: 'blur'}
  ],
  eigenvalueTypeList: [
    {required: true, message: '特征值种类不可为空', trigger: 'blur'}
  ],
}

export const editSensor_set_rule = {
  content: [
    {required: true, message: '传感器名称不可为空', trigger: 'blur'}
  ],
  installTime: [
    {required: true, message: '安装时刻不可为空', trigger: 'blur'}
  ],
  windowLength: [
    {required: true, message: '窗口长度不可为空', trigger: 'blur'}
  ],
  upLimit: [
    {required: true, message: '上限不可为空', trigger: 'blur'}
  ],
  downLimit: [
    {required: true, message: '下限不可为空', trigger: 'blur'}
  ],
}
