<template>
  <div class="map-detail" :style="{ top: top + 'px', left: left + 'px' }" @click.stop="">
    <section class="header">
      <div class="header-title">
        <span></span>
        <span>{{ title }}</span>
      </div>
      <i class="el-icon-close close" @click="onClose"></i>
    </section>
    <section class="body">
      <video src=""></video>
    </section>
  </div>
</template>

<script>
export default {
  name: 'MapDetail',
  props: {
    top: {
      type: [Number, String],
      default: 100
    },
    left: {
      type: [Number, String],
      default: 600
    },
    title: {
      type: String,
      default: '查看视频'
    },
    form: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      
    }
  },
  methods: {
    onClose() {
      this.$emit('close');
    },
    playVideo() {
      console.log(this.form)
    }
  }
}
</script>

<style lang="scss" scoped>
.map-detail {
  width: 320px;
  height: 239px;
  background: rgba(4, 17, 48, 0.8);
  box-shadow: inset 0px 0px 10px 0px #3662EC;
  border-radius: 10px;
  border: 1px solid #0687FF;
  position: fixed;
  z-index: 100;
  overflow: hidden;

  .header {
    width: 100%;
    height: 42px;
    background: linear-gradient(90deg, rgba(6, 97, 255, 0.312) 0%, rgba(6, 97, 255, 0) 100%);
    border-radius: 0px 0px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;

    .header-title {
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 16px;

      span:first-child {
        margin-right: 10px;
        width: 3px;
        height: 20px;
        background: #F2AF4A;
      }
    }

    .close {
      margin-left: auto;
      font-size: 18px;
      color: #ffffff;
      cursor: pointer;
    }
  }

  .body {
    width: 100%;
    height: calc(100% - 42px);
    padding: 10px 20px;
    box-sizing: border-box;
    overflow: hidden;
  }
}
</style>